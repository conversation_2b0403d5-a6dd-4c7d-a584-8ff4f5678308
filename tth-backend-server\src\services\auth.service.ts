import { formatUserAgentData, generateEmploymentN<PERSON>ber, getOrganizationLogo, getOrgName, sendEmailNotification, sendInvitation } from "../helper/common";
import { encrypt } from "../helper/utils";
import { Role } from "../models/MORole";
import { Role as uRole } from "../models/Role";
import { MOPermission } from "../models/MOPermission";
import { MOModule } from "../models/MOModule";
import { User, user_status } from "../models/User";
import { invitation_status, UserInvite } from "../models/UserInvite";
import { UserRole } from "../models/UserRole";


import { UserSession } from "../models/UserSession";
import { Activity } from "../models/Activity";
import { BannerNotification } from "../models/BannerNotification";
import { BannerConfig } from "../models/BannerConfig";
import { EMAIL_ADDRESS, ROLE_PERMISSIONS } from "../helper/constant";
import { ChangeRequestSettings } from "../models/ChangeRequestSettings";
import { Setting, setting_status } from "../models/Setting";
import { Op } from "sequelize";

/** Store staff details */
const staffUpdateDetails = async (staffData: any) => {
    try {
        staffData = staffData.staffResponse;

        /** check if staff action is reset password */
        if (staffData.type == 'reset_password') {
            const getUserData: any = await User.findOne({ where: { keycloak_auth_id: staffData.userId }, attributes: ['id', 'user_status'], raw: true })
            if (getUserData) {
                /** check user status, if it's pending then update with active status */
                await User.update({
                    user_status:
                        getUserData.user_status == user_status.PENDING
                            ? user_status.ACTIVE
                            : getUserData.user_status,
                }, { where: { id: getUserData.id } })
                const findUserInvitation = await UserInvite.findOne({ where: { user_id: getUserData.id, invitation_status: { [Op.not]: invitation_status.ACCEPTED } } })
                if (findUserInvitation) {
                    await UserInvite.update({ invitation_status: invitation_status.ACCEPTED, action_by: getUserData.id, updated_by: getUserData.id }, { where: { id: findUserInvitation.id } })
                }
            }
        } else {
            /** Update keycloak auth id after creation staff user. */
            if (staffData) {
                const username = staffData.username ? staffData.username : null
                await User.update({ keycloak_auth_id: staffData.keycloak_auth_id, username: username }, {
                    where: {
                        id: staffData.userId
                    }
                })
            }
        }
    } catch (e: any) {
        console.error('Error in staffUpdateDetails:', e);
        return { status: false, message: e }
    }
}
/** Store org master details */
const orgMasterDetails = async (masterData: any) => {
    try {
        masterData = masterData.orgMasterData;
        const employmentNumber = await generateEmploymentNumber(masterData.organization_id)

        if (masterData) {

            const superAdminRole = await createDefaultRolesAndPermissions(masterData.organization_id, 1);

            /** Prepare User object and store data */
            const createObj: any = {
                user_first_name: masterData.firstName,
                user_last_name: masterData.lastName,
                user_email: masterData.email,
                user_phone_number: masterData.phoneNumber,
                user_status: 'pending',
                keycloak_auth_id: masterData.keycloak_auth_id,
                organization_id: masterData.organization_id,
                user_password: await encrypt(masterData.userPassword),
                employment_number: employmentNumber,
                user_active_role_id: superAdminRole.id,
                web_user_active_role_id: superAdminRole.id,
                username: masterData.username,
                user_designation: masterData.user_designation
            }
            const createUser = await User.create(createObj)

            /** Prepare User role object and store data */
            // const createUserRole: any = {
            //     user_id: createUser.id,
            //     role_id: superAdminRole.id
            // }
            // await UserRole.create(createUserRole);
            /** Create Default change request setting for organization */
            const createChangeRequestSetting: any = {
                organization_id: masterData.organization_id,
                key: JSON.stringify(["user_first_name", "user_last_name", "user_email"]),
                created_by: createUser.id,
                updated_by: createUser.id
            }
            await ChangeRequestSettings.create(createChangeRequestSetting);

            /** create default settings for organization */
            const defaultSettings: any = [
                { key: "base_leave", value: "5.6" },
                { key: "working_hours_per_day", value: "7" },
                { key: "max_limit_per_week", value: "35" },
                { key: "financial_month", value: "april - march" },
                { key: "currency", value: JSON.stringify({ currency: "GBP", name: "British pound", symbol: "£" }) },
                { key: "leave_calculation_type", value: "manual" },
                { key: "leave_period_type", value: "day" },
            ];

            const settingsToInsert: any = defaultSettings.map((setting: any) => ({
                ...setting,
                organization_id: masterData.organization_id,
                setting_status: setting_status.ACTIVE,
                created_by: createUser.id,
                updated_by: createUser.id
            }));

            await Setting.bulkCreate(settingsToInsert);
        }

    } catch (e: any) {
        console.error('Error in orgMasterDetails:', e);
        return { status: false, message: e }
    }
}
/** Update org master status from pending to verified  */
const updateOrgMasterStatus = async (masterData: any) => {
    try {
        masterData = masterData.orgMaster;

        /** check masterData exist then update status from pending to verified */
        if (masterData.type == 'update_super_admin') {
            const updateObj: any = {
                username: masterData.username,
                user_email: masterData.user_email,
                user_first_name: masterData.user_first_name,
                user_last_name: masterData.user_last_name,
                user_phone_number: masterData.user_phone_number,
            }
            const getUserData: any = await User.update(updateObj, { where: { keycloak_auth_id: masterData.keycloak_auth_id } })
            if (getUserData) {
                return { status: true, message: 'Super admin updated successfully' }
            }
            return { status: false, message: 'Super admin not found' }
        } else {
            await User.update({ user_status: user_status.VERIFIED }, {
                where: {
                    keycloak_auth_id: masterData.keycloak_auth_id
                }
            })
        }
    } catch (e: any) {
        console.error('Error in updateOrgMasterStatus:', e);
        return { status: false, message: e }
    }
}
/** staff creation mail */
const staffCreationMail = async (mailData: any) => {
    try {
        mailData = mailData.mailResponse;
        if (mailData) {
            /** check if consumer queue id for staff reinvitation then execute below code */
            if (mailData && mailData.staffData) {
                const staffData: any = mailData.staffData;
                if (staffData.email) {
                    const templateData = {
                        name: staffData.name,
                        email: staffData.email,
                        password: staffData.user_password,
                        role: staffData.role,
                        username: mailData.username,
                        mail_type: 'send_invitation',
                        ORGANIZATION_LOGO: await getOrganizationLogo(mailData.organization_id),
                        LOGO: global.config.API_UPLOAD_URL + "/email_logo/logo.png",
                        ADDRESS: EMAIL_ADDRESS.ADDRESS,
                        PHONE_NUMBER: EMAIL_ADDRESS.PHONE_NUMBER,
                        EMAIL: EMAIL_ADDRESS.EMAIL,
                        ORGANIZATION_NAME: EMAIL_ADDRESS.ORGANIZATION_NAME,
                        smtpConfig: 'INFO',
                        organization: await getOrgName(mailData.organization_id)
                    };
                    await sendEmailNotification(templateData)
                }
            } else {
                /** below code is first time invitation of staff */
                const addUserId = mailData.staffUserId
                const adminId = mailData.adminId
                const password = mailData.password
                const username = mailData.username
                /** Send mail */
                await sendInvitation([addUserId], adminId, username, password)
            }
        }
    } catch (e: any) {
        console.error('Error in staffCreationMail:', e);
        return { status: false, message: e }
    }
}
/** session stored data */
const sessionStorage = async (loginData: any) => {
    try {
        const sessionData = loginData.sessionData
        const fetchUser: any = await User.findOne({ where: { keycloak_auth_id: sessionData.user_id }, attributes: ['id'], raw: true })
        if (fetchUser) {
            const userRoles: any = await UserRole.findAll({
                where: { user_id: fetchUser.id },
                include: [
                    {
                        model: uRole,
                        as: "role",
                        attributes: ["id", "role_name"],
                    },
                ],
                nest: true,
                raw: true,
            });
            const roleData = userRoles[0]

            if (process.env.NEXT_NODE_ENV !== "staging" && roleData.role.name !== 'Super Admin') {
                await UserSession.destroy({ where: { user_id: fetchUser.id, device_type: sessionData.device_type } });
                // Store the new session in the database
                await UserSession.create({ user_id: fetchUser.id, device_type: sessionData.device_type, token: sessionData.token } as any);
            }
        }
    } catch (e: any) {
        console.error('Error in sessionStorage:', e);
        return { status: false, message: e }
    }
}

/** store activity log */
const storeActivityLog = async (task: any) => {
    try {
        const data = task.logData.message
        const headers = task.logData.header
        const { activity_table, activity_action, message, platformType, reference_id, organization_id, keycloak_userId, activity_type } = data;
        /** let Get userData */
        const userData: any = await User.findOne({ where: { keycloak_auth_id: keycloak_userId ? keycloak_userId : message.keycloak_userId }, attributes: ['id'], raw: true })

        await Activity.create({
            activity_table,
            activity_action,
            reference_id: reference_id ? reference_id : userData?.id,
            ip_address: headers?.["ip-address"],
            address: headers?.["address"],
            userAgent: `${message?.platformType}` ? `${formatUserAgentData(headers?.["user-agent"], message?.platformType)}` : platformType,
            location: headers?.["location"],
            previous_data: JSON.stringify(data?._previousDataValues),
            new_data: JSON.stringify(message),
            organization_id: organization_id ? organization_id : message.organization_id,
            activity_type: activity_type,
            created_by: userData?.id || null,
            updated_by: userData?.id || null,
        } as any)
    } catch (e: any) {
        console.error('Error in storeActivityLog:', e);
        return { status: false, message: e }
    }
}

/** store Banner Notifications */
const storeBannerNotification = async (notificationData: any) => {
    try {
        const whereObj: any = {};
        /** check if plan status is trial_plan or renew_plan then set key as subscription */
        if (notificationData.plan_status == 'trial_plan' || notificationData.plan_status == 'renew_plan') {
            whereObj.key = 'subscription'
        }

        if (notificationData.plan_status == 'cancel_plan') {
            whereObj.key = 'subscription_cancel'
        }

        const bannerConfig: any = await BannerConfig.findOne({ where: whereObj, raw: true })
        let createObj: any = {}
        if (bannerConfig) {
            createObj.banner_config_id = bannerConfig.id
        }
        createObj = { ...createObj, ...notificationData }
        await BannerNotification.create(createObj as any)
    } catch (e: any) {
        console.error('Error in storeBannerNotification:', e);
        return { status: false, message: e }
    }
}


const getRolesToCreate = () => {
    return [
        { id: 1, role_name: 'Super Admin', parent_role_id: null },
        { id: 2, role_name: 'Admin', parent_role_id: 1 },
        { id: 3, role_name: 'Director', parent_role_id: 2 },
        { id: 4, role_name: 'HR', parent_role_id: 2 },
        { id: 5, role_name: 'Area Manager', parent_role_id: 4 },
        { id: 6, role_name: 'Accountant', parent_role_id: 5 },
        { id: 7, role_name: 'Branch Manager', parent_role_id: 6 },
        { id: 8, role_name: 'Assist. Branch Manager', parent_role_id: 7 },
        { id: 9, role_name: 'Head Chef', parent_role_id: 7 },
        { id: 10, role_name: 'Bar Manager', parent_role_id: 7 },
        { id: 11, role_name: 'FOH', parent_role_id: 7 },
        { id: 12, role_name: 'Bar', parent_role_id: 7 },
        { id: 13, role_name: 'Kitchen', parent_role_id: 7 },
        { id: 14, role_name: 'Hotel Manager', parent_role_id: 6 },
        { id: 15, role_name: 'Assist. Hotel Manager', parent_role_id: 14 },
        { id: 16, role_name: 'Receptionist', parent_role_id: 15 },
        { id: 17, role_name: 'Head Housekeeper', parent_role_id: 15 },
        { id: 18, role_name: 'House Keeper', parent_role_id: 17 },
        { id: 19, role_name: 'Signature', parent_role_id: 2 },
    ];
}

// Ensure view permission is added if create/edit/delete is present
export const setViewPermission = (permission: number) => {
    if (permission & (ROLE_PERMISSIONS.CREATE | ROLE_PERMISSIONS.EDIT | ROLE_PERMISSIONS.DELETE)) {
        permission |= ROLE_PERMISSIONS.VIEW;  // Add view permission if create/edit/delete is present
    }
    return permission;
};

const createDefaultRolesAndPermissions = async (organization_id: string, user_id: number) => {
    try {
        const modulesToCreate = [
            { id: 1, module: 'dashboard', module_name: 'Dashboard' },
            { id: 2, module: 'budget_forecast', module_name: 'Budget & Forecast' },
            { id: 3, module: 'company_setting', module_name: 'Comapny Settings' },
            { id: 4, module: 'branch', module_name: 'Branches' },
            { id: 5, module: 'training', module_name: 'Training Assignments' },
            { id: 6, module: 'dsr_setting', module_name: 'DSR settings' },
            { id: 7, module: 'department', module_name: 'Departments' },
            { id: 8, module: 'holiday_management', module_name: 'Holiday Management' },
            { id: 9, module: 'leave_setting', module_name: 'Leave Configuration' },
            { id: 10, module: 'administrator_account', module_name: 'Administrator Accounts' },
            { id: 11, module: 'export_setting', module_name: 'Export Settings' },
            { id: 12, module: 'change_request_setting', module_name: 'Change Request' },
            { id: 13, module: 'staff', module_name: 'All staff' },
            { id: 14, module: 'staff_invitation', module_name: 'Staff Invitation' },
            { id: 15, module: 'employee_contract', module_name: 'Contract Renewal' },
            { id: 16, module: 'change_request', module_name: 'Change Request' },
            { id: 17, module: 'resignation', module_name: 'Resignation' },
            { id: 18, module: 'dsr', module_name: 'DSR' },
            { id: 19, module: 'dsr_request', module_name: 'DSR Request' },
            { id: 20, module: 'wsr', module_name: 'WSR' },
            { id: 21, module: 'wsr_request', module_name: 'WSR Request' },
            { id: 22, module: 'expense', module_name: 'Expenses' },
            { id: 23, module: 'expense_request', module_name: 'Expenses Request' },
            { id: 24, module: 'dsr_report', module_name: 'Logbook Reports' },
            { id: 25, module: 'team_leave', module_name: 'Team Leave' },
            { id: 26, module: 'my_leave', module_name: 'My Leave' },
            { id: 27, module: 'leave_report', module_name: 'Leave Reports' },
            { id: 28, module: 'rota_dashboard', module_name: 'Dashboard' },
            { id: 29, module: 'rotas', module_name: 'Rotas' },
            { id: 30, module: 'availability', module_name: 'Availability' },
            { id: 31, module: 'staff_notification', module_name: 'Staff view' },
            { id: 32, module: 'own_notification', module_name: 'Personal view' },
            { id: 33, module: 'staff_document', module_name: 'Staff view' },
            { id: 34, module: 'own_document', module_name: 'Personal view' },
            { id: 35, module: 'activity_log', module_name: 'Activity Logs' },
            { id: 36, module: 'permission', module_name: 'Roles Permissions' },
            { id: 37, module: 'role', module_name: 'Roles' },
            { id: 38, module: 'module', module_name: 'Modules' },
        ];

        const moduleIdMap = new Map<number, number>();
        for (const moduleData of modulesToCreate) {
            let module = await MOModule.findOne({ where: { module: moduleData.module, organization_id: organization_id } });
            if (!module) {
                module = await MOModule.create({ ...moduleData, id: undefined, organization_id: organization_id, created_by: user_id, updated_by: user_id } as any);
            }
            moduleIdMap.set(moduleData.id, module.id);
        }

        const rolesToCreate = getRolesToCreate();
        const roleIdMap = new Map<number, number>();
        let superAdminRole: any;

        for (const roleData of rolesToCreate) {
            const parentRoleId = roleData.parent_role_id ? roleIdMap.get(roleData.parent_role_id) : null;

            let role = await Role.findOne({ where: { role_name: roleData.role_name, organization_id: organization_id } });
            if (!role) {
                role = await Role.create({
                    role_name: roleData.role_name,
                    role_status: 'active',
                    parent_role_id: parentRoleId,
                    organization_id: organization_id,
                    created_by: user_id,
                    updated_by: user_id,
                } as any);
            }

            roleIdMap.set(roleData.id, role.id);

            if (roleData.role_name === 'Super Admin') {
                superAdminRole = role;
            }

            const permissions = await MOPermission.findAll({
                where: { role_id: roleData.id, organization_id: null },
                raw: true,
            } as any);

            for (const permission of permissions) {
                const newModuleId = moduleIdMap.get(permission.module_id);
                if (newModuleId) {
                    const existingPermission = await MOPermission.findOne({ where: { role_id: role.id, module_id: newModuleId, organization_id: organization_id } });
                    if (!existingPermission) {
                        await MOPermission.create({
                            ...permission,
                            id: undefined,
                            role_id: role.id,
                            module_id: newModuleId,
                            organization_id: organization_id,
                            created_by: user_id,
                            updated_by: user_id,
                            permission: roleData.role_name === 'Super Admin' ? 15 : setViewPermission(permission.permission)
                        } as any);
                    }
                }
            }
        }
        return superAdminRole;
    } catch (e: any) {
        console.error('Error in createDefaultRolesAndPermissions:', e);
        throw e;
    }
}



const backfillDefaultData = async () => {
    try {
        const organizations = await User.findAll({
            attributes: ['organization_id'],
            group: ['organization_id'],
            where: { organization_id: { [Op.ne]: null } },
            raw: true,
        } as any);

        for (const org of organizations) {
            if (org.organization_id) {
                const user = await User.findOne({ 
                    where: { organization_id: org.organization_id }, 
                    include: { 
                        model: UserRole, 
                        as: 'user_roles',
                        include: [{
                            model: uRole, 
                            as: 'role', 
                            where: { role_name: 'Super Admin' } 
                        }]
                    }, raw: true 
                });
                if (user) {
                    const newRole = await createDefaultRolesAndPermissions(org.organization_id, 1);
                    await User.update({ user_active_role_id: newRole.id, web_user_active_role_id: newRole.id }, { where: { id: user.id } })
                }
            }
        }
    } catch (e: any) {
        console.error('Error in backfillDefaultData:', e);
        throw e;
    }
}

setTimeout(() => {
    backfillDefaultData().then().catch((e: any) => {
        console.error('Error in backfillDefaultData:', e);
    })
}, 100000)

export { staffUpdateDetails, orgMasterDetails, updateOrgMasterStatus, staffCreationMail, sessionStorage, storeActivityLog, storeBannerNotification, createDefaultRolesAndPermissions, backfillDefaultData }
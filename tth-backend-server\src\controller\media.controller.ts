import { StatusCodes } from "http-status-codes";
import { Category, category_status } from "../models/Category";
import { Media, media_status } from "../models/Media";
import { Op } from "sequelize";
import { getPaginatedItems, getPagination } from "../helper/utils";
// import path from "path";
// import fs from "fs-extra";
import { sequelize } from "../models";
import mediaValidator from "../validators/media.validator";
import { Playlist, playlist_status } from "../models/Playlist";
import {
  PlaylistCategory,
  playlist_category_status,
} from "../models/PlaylistCategory";
import { PlaylistMedia, playlist_media_status } from "../models/PlaylistMedia";
import {
  PlaylistBranch,
  playlist_branch_status,
} from "../models/PlaylistBranch";
import {
  PlaylistDepartment,
  playlist_department_status,
} from "../models/PlaylistDepartment";
import { Branch, branch_status } from "../models/Branch";
import { Department, department_status } from "../models/Department";
import {
  PlaylistMediaTrack,
  playlist_media_track_status,
} from "../models/PlaylistMediaTrack";
import { User } from "../models/User";
import { Role, role_status } from "../models/Role";
import { NORMAL_USER, NOTIFICATION_TYPE, REDIRECTION_TYPE, ROLE_CONSTANT, NOTIFICATIONCONSTANT, FILE_UPLOAD_CONSTANT } from "../helper/constant";
import { sendPushNotification } from "../helper/notification.service";
import {
  deleteFileFromBucket,
  moveFileInBucket,
  // s3,
} from "../helper/upload.service";
import { Item, item_status } from "../models/Item";
// import { PutObjectCommand } from "@aws-sdk/client-s3";
/**
 *  Create category
 * @param req
 * @param res
 * @returns
 */

const createCategory = async (req: any, res: any) => {
  try {
    const {
      category_name,
      category_description,
      categoryStatus,
      dashboard_view = false,
    } = req.body;

    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const findCategoryExist = await Category.findOne({
      where: { category_name: category_name },
    });
    if (
      findCategoryExist &&
      findCategoryExist.category_status == category_status.ACTIVE
    ) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CATEGORY_EXIST") });
    }

    if (
      findCategoryExist &&
      findCategoryExist.category_status == category_status.INACTIVE
    ) {
      const updateExistingCategory = await Category.setHeaders(req).update(
        { category_status: categoryStatus, updated_by: req.user.id },
        { where: { id: findCategoryExist.id } },
      );
      if (updateExistingCategory.length) {
        return res
          .status(StatusCodes.OK)
          .json({ status: true, message: res.__("SUCCESS_CATEGORY_CREATED") });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: true, message: res.__("FAIL_CATEGORY_CREATION") });
      }
    }
    const addNewCategory = await Category.setHeaders(req).create({
      category_name,
      category_description,
      category_status: categoryStatus,
      dashboard_view: dashboard_view,
      created_by: req.user.id,
      updated_by: req.user.id,
    } as any);
    if (addNewCategory) {
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_CATEGORY_CREATED") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: true, message: res.__("FAIL_CATEGORY_CREATION") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Update category
 * @param req
 * @param res
 * @returns
 */

const updateCategory = async (req: any, res: any) => {
  try {
    const {
      category_name,
      category_description,
      categoryStatus,
      dashboard_view,
    } = req.body;
    const { category_id } = req.params;
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const findCategoryExist = await Category.findOne({
      where: {
        id: category_id,
        category_status: { [Op.not]: category_status.DELETED },
      },
    });

    if (findCategoryExist) {
      const updateExistingCategory = await Category.setHeaders(req).update(
        {
          category_name: category_name,
          category_description: category_description,
          category_status: categoryStatus,
          dashboard_view: dashboard_view,
          updated_by: req.user.id,
        },
        { where: { id: findCategoryExist.id } },
      );
      if (updateExistingCategory.length) {
        return res
          .status(StatusCodes.OK)
          .json({ status: true, message: res.__("SUCCESS_CATEGORY_UPDATED") });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: true, message: res.__("FAIL_CATEGORY_UPDATION") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete category
 * @param req
 * @param res
 * @returns
 */

const deleteCategory = async (req: any, res: any) => {
  try {
    const { category_id } = req.params;
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const findCategoryExist = await Category.findOne({
      where: {
        id: category_id,
        category_status: { [Op.not]: category_status.DELETED },
      },
    });

    if (findCategoryExist) {
      const findPlaylistExist = await PlaylistCategory.findOne({
        where: {
          category_id: findCategoryExist?.id,
          playlist_category_status: playlist_category_status.ACTIVE,
        },
      });

      if (findPlaylistExist) {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("FAIL_PLAYLIST_ALREADY_EXIST"),
        });
      }

      const updateExistingCategory = await Category.setHeaders(req).update(
        { category_status: category_status.DELETED, updated_by: req.user.id },
        { where: { id: findCategoryExist.id } },
      );
      if (updateExistingCategory.length) {
        return res
          .status(StatusCodes.OK)
          .json({ status: true, message: res.__("SUCCESS_CATEGORY_DELETED") });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: true, message: res.__("FAIL_CATEGORY_DELETATION") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get category list
 * @param req
 * @param res
 * @returns
 */

const getCategoryList = async (req: any, res: any) => {
  try {
    const { search, size, page, CategoryStatus, view_type } = req.query;

    const whereObj: any = {};
    if (CategoryStatus == category_status.ACTIVE) {
      whereObj.category_status = category_status.ACTIVE;
    } else {
      whereObj.category_status = { [Op.not]: category_status.DELETED };
      if (req.headers["platform-type"] != "web") {
        whereObj.category_status = category_status.ACTIVE;
        whereObj[Op.and] = [
          sequelize.literal(`(
            SELECT
              count(*)
            FROM nv_playlist_media
            WHERE playlist_media_status = 'active' 
                AND playlist_id IN ( 
                SELECT id FROM nv_playlist 
                WHERE id IN (SELECT playlist_id FROM nv_playlist_category WHERE category_id = Category.id AND playlist_category_status = 'active' AND playlist_id = nv_playlist.id) 
                AND id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id =  ${req.user.branch_id} AND playlist_branch_status = 'active' AND playlist_id = nv_playlist.id) 
                ${view_type == "own" || !view_type ? `AND id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = ${req.user.department_id} AND playlist_department_status = 'active' AND playlist_id = nv_playlist.id)` : ""} 
                AND playlist_status = 'active'
            )
        ) > 0`),
        ];
      }
    }

    if (search) {
      whereObj.category_name = { [Op.like]: `%${search}%` };
    }
    const categoryObj: any = {
      where: whereObj,
      attributes: [
        "id",
        "category_name",
        "category_description",
        "category_status",
        "dashboard_view",
        [
          sequelize.literal(
            `(
              SELECT
              (CASE
                WHEN ((ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))) = 100) THEN 'completed'
                WHEN ((ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))) = 0) THEN 'pending'
                ELSE 'ongoing'
              END)
              FROM nv_playlist_media
              WHERE playlist_media_status = 'active' 
                  AND playlist_id IN ( SELECT id FROM nv_playlist WHERE id IN( SELECT
                          playlist_id
                      FROM
                          nv_playlist_category
                      WHERE
                          category_id = Category.id AND playlist_category_status = 'active'
                  ) AND id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id =  ${req.user.branch_id} AND playlist_branch_status = 'active') AND 
                  id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = ${req.user.department_id} AND playlist_department_status = 'active') 
                  AND playlist_status = 'active'
              )
          )`,
          ),
          "category_pl_track_status",
        ],
        [
          sequelize.literal(
            `(
              SELECT
                ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))
              FROM nv_playlist_media
              WHERE playlist_media_status = 'active' 
                  AND playlist_id IN ( SELECT id FROM nv_playlist WHERE id IN( SELECT
                          playlist_id
                      FROM
                          nv_playlist_category
                      WHERE
                          category_id = Category.id AND playlist_category_status = 'active'
                  ) AND id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id =  ${req.user.branch_id} AND playlist_branch_status = 'active') AND 
                  id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = ${req.user.department_id} AND playlist_department_status = 'active') 
                  AND playlist_status = 'active'
              )
          )`,
          ),
          "category_pl_track_percentage",
        ],
      ],
    };
    const { limit, offset } = getPagination(Number(page), Number(size));

    if (size && page) {
      (categoryObj.limit = Number(limit)),
        (categoryObj.offset = Number(offset));
    }
    (categoryObj.nest = true), (categoryObj.raw = true);
    const { count, rows: getCategoryList } =
      await Category.findAndCountAll(categoryObj);
    const { total_pages } = getPaginatedItems(size, page, count || 0);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getCategoryList,
      page: parseInt(page),
      size: parseInt(size),
      total_pages,
      count: count || 0,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Create playlist
 * @param req
 * @param res
 * @returns
 */

const createPlaylist = async (req: any, res: any) => {
  try {
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const { error } = await mediaValidator.createPlaylist.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const { playlist_name, playlist_description, playlistStatus } = req.body;
    const category_id = req.body.category_id
      ? req.body.category_id.split(",").map(Number)
      : [];
    const branch_id = req.body.branch_id
      ? req.body.branch_id.split(",").map(Number)
      : [];
    const department_id = req.body.department_id
      ? req.body.department_id.split(",").map(Number)
      : [];

    const findPlayListExist = await Playlist.findOne({
      where: { playlist_name: playlist_name },
    });

    if (
      findPlayListExist &&
      findPlayListExist.playlist_status !== playlist_status.DELETED
    ) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("PLAYLIST_ALREADY_EXIST") });
    }

    const findCategoryExist = await Category.findAll({
      where: {
        id: { [Op.in]: category_id },
        category_status: category_status.ACTIVE,
      },
    });

    if (findCategoryExist.length != category_id.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CATEGORY_NOT_FOUND") });
    }

    const findBranchExist = await Branch.findAll({
      where: {
        id: { [Op.in]: branch_id },
        branch_status: branch_status.ACTIVE,
      },
    });

    if (findBranchExist.length != branch_id.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }

    const findDepartmentExist = await Department.findAll({
      where: {
        id: { [Op.in]: department_id },
        department_status: department_status.ACTIVE,
      },
    });

    if (findDepartmentExist.length != department_id.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("DEPARTMENT_NOT_FOUND") });
    }

    // Handle file upload for playlist image
    let playlistImageId = null;
    if (req.files && req.files.length > 0) {
      playlistImageId = req.files[0].item_id;

      // Move file to proper location if needed
      if (req.files[0].isMovable) {
        await moveFileInBucket(
          req.files[0].bucket,
          req.files[0].path,
          FILE_UPLOAD_CONSTANT.MEDIA_FILES.destinationPath(
            req.user.organization_id,
            req.files[0].filename,
          ),
          req.files[0].item_id,
          true,
        );
      }
    }

    if (
      findPlayListExist &&
      findPlayListExist.playlist_status == playlist_status.DELETED
    ) {
      const updatePlaylist = await Playlist.setHeaders(req).update(
        {
          playlist_status: playlistStatus,
          playlist_name,
          playlist_description,
          updated_by: req.user.id,
          playlist_image: playlistImageId || "",
        },
        { where: { id: findPlayListExist.id } },
      );

      if (updatePlaylist.length > 0) {
        for (let i = 0; category_id.length > i; i++) {
          const findPlaylistCategory = await PlaylistCategory.findOne({
            where: {
              category_id: category_id[i],
              playlist_id: findPlayListExist.id,
            },
          });

          if (findPlaylistCategory) {
            await PlaylistCategory.setHeaders(req).update(
              {
                updated_by: req.user.id,
                playlist_category_status: findPlayListExist.playlist_status,
              },
              {
                where: {
                  category_id: findPlaylistCategory.category_id,
                  playlist_id: findPlaylistCategory.playlist_id,
                },
              },
            );
          } else {
            await PlaylistCategory.setHeaders(req).create({
              category_id: category_id[i],
              playlist_id: findPlayListExist.id,
              order: i + 1,
              created_by: req.user.id,
              updated_by: req.user.id,
              playlist_category_status: findPlayListExist.playlist_status,
            } as any);
          }
        }
        for (let i = 0; branch_id.length > i; i++) {
          const findPlaylistBranch = await PlaylistBranch.findOne({
            where: {
              branch_id: branch_id[i],
              playlist_id: findPlayListExist.id,
            },
          });
          if (findPlaylistBranch) {
            await PlaylistBranch.setHeaders(req).update(
              {
                updated_by: req.user.id,
                playlist_branch_status: findPlayListExist.playlist_status,
              },
              {
                where: {
                  branch_id: findPlaylistBranch.branch_id,
                  playlist_id: findPlaylistBranch.playlist_id,
                },
              },
            );
          } else {
            await PlaylistBranch.setHeaders(req).create({
              branch_id: branch_id[i],
              playlist_id: findPlayListExist.id,
              created_by: req.user.id,
              updated_by: req.user.id,
              playlist_branch_status: findPlayListExist.playlist_status,
            } as any);
          }
        }
        for (let i = 0; department_id.length > i; i++) {
          const findPlaylistDepartment = await PlaylistDepartment.findOne({
            where: {
              department_id: department_id[i],
              playlist_id: findPlayListExist.id,
            },
          });
          if (findPlaylistDepartment) {
            await PlaylistDepartment.setHeaders(req).update(
              {
                updated_by: req.user.id,
                playlist_department_status: findPlayListExist.playlist_status,
              },
              {
                where: {
                  department_id: findPlaylistDepartment.department_id,
                  playlist_id: findPlaylistDepartment.playlist_id,
                },
              },
            );
          } else {
            await PlaylistDepartment.setHeaders(req).create({
              department_id: department_id[i],
              playlist_id: findPlayListExist.id,
              created_by: req.user.id,
              updated_by: req.user.id,
              playlist_department_status: findPlayListExist.playlist_status,
            } as any);
          }
        }
        return res
          .status(StatusCodes.OK)
          .json({ status: true, message: res.__("SUCCESS_PLAYLIST_CREATED") });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: false, message: res.__("FAIL_PLAYLIST_CREATION") });
      }
    }

    const addPlaylist = await Playlist.setHeaders(req).create({
      playlist_image: playlistImageId || "",
      playlist_name,
      playlist_description,
      playlist_status: playlistStatus,
      created_by: req.user.id,
      updated_by: req.user.id,
    } as any);

    if (addPlaylist) {
      for (let i = 0; category_id.length > i; i++) {
        await PlaylistCategory.setHeaders(req).create({
          category_id: category_id[i],
          playlist_id: addPlaylist.id,
          order: i + 1,
          created_by: req.user.id,
          updated_by: req.user.id,
          playlist_category_status: addPlaylist.playlist_status,
        } as any);
      }
      for (let i = 0; branch_id.length > i; i++) {
        await PlaylistBranch.setHeaders(req).create({
          branch_id: branch_id[i],
          playlist_id: addPlaylist.id,
          created_by: req.user.id,
          updated_by: req.user.id,
          playlist_branch_status: addPlaylist.playlist_status,
        } as any);
      }
      for (let i = 0; department_id.length > i; i++) {
        await PlaylistDepartment.setHeaders(req).create({
          department_id: department_id[i],
          playlist_id: addPlaylist.id,
          created_by: req.user.id,
          updated_by: req.user.id,
          playlist_department_status: addPlaylist.playlist_status,
        } as any);
      }
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_PLAYLIST_CREATED") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("FAIL_PLAYLIST_CREATION") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Update playlist
 * @param req
 * @param res
 * @returns
 */

const updatePlaylist = async (req: any, res: any) => {
  try {
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const { playlist_id } = req.params;
    const { playlist_name, playlist_description, playlistStatus } = req.body;
    const category_id = req.body.category_id
      ? req.body.category_id.split(",").map(Number)
      : [];
    const branch_id = req.body.branch_id
      ? req.body.branch_id.split(",").map(Number)
      : [];
    const department_id = req.body.department_id
      ? req.body.department_id.split(",").map(Number)
      : [];
    const { error: fileError } = await mediaValidator.playlist_image.validate(
      req.file,
    );

    if (fileError) {
      return res
        .status(400)
        .json({ status: false, message: fileError.details[0].message });
    }
    const { error } = await mediaValidator.updatePlaylist.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }

    const checkPlayListExist = await Playlist.findOne({
      where: {
        id: playlist_id,
        playlist_status: { [Op.not]: playlist_status.DELETED },
      },
    });

    if (!checkPlayListExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("PLAYLIST_NOT_FOUND") });
    }

    const findBranchExist = await Branch.findAll({
      where: {
        id: { [Op.in]: branch_id },
        branch_status: branch_status.ACTIVE,
      },
    });

    if (findBranchExist.length != branch_id.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }

    const findDepartmentExist = await Department.findAll({
      where: {
        id: { [Op.in]: department_id },
        department_status: department_status.ACTIVE,
      },
    });

    if (findDepartmentExist.length != department_id.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("DEPARTMENT_NOT_FOUND") });
    }

    const findCategoryExist = await Category.findAll({
      where: {
        id: { [Op.in]: category_id },
        category_status: category_status.ACTIVE,
      },
    });

    if (findCategoryExist.length != findCategoryExist.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CATEGORY_NOT_FOUND`") });
    }

    // Handle file upload for playlist image
    let playlistImageId = checkPlayListExist.playlist_image;
    if (req.files && req.files.length > 0) {
      playlistImageId = req.files[0].item_id;

      // Move file to proper location if needed
      if (req.files[0].isMovable) {
        await moveFileInBucket(
          req.files[0].bucket,
          req.files[0].path,
          FILE_UPLOAD_CONSTANT.MEDIA_FILES.destinationPath(
            req.user.organization_id,
            req.files[0].filename,
          ),
          req.files[0].item_id,
          true,
        );
      }
    }

    const updatePlaylist = await Playlist.setHeaders(req).update(
      {
        playlist_image: playlistImageId,
        playlist_name,
        playlist_description,
        playlist_status: playlistStatus,
        updated_by: req.user.id,
      },
      { where: { id: checkPlayListExist.id } },
    );
    if (updatePlaylist.length > 0) {
      for (let i = 0; category_id.length > i; i++) {
        const findPlaylistCategory = await PlaylistCategory.findOne({
          where: {
            category_id: category_id[i],
            playlist_id: checkPlayListExist.id,
          },
        });

        if (findPlaylistCategory) {
          await PlaylistCategory.setHeaders(req).update(
            {
              updated_by: req.user.id,
              playlist_category_status: checkPlayListExist.playlist_status,
            },
            {
              where: {
                category_id: findPlaylistCategory.category_id,
                playlist_id: findPlaylistCategory.playlist_id,
              },
            },
          );
        } else {
          await PlaylistCategory.setHeaders(req).create({
            category_id: category_id[i],
            playlist_id: checkPlayListExist.id,
            order: i + 1,
            created_by: req.user.id,
            updated_by: req.user.id,
            playlist_category_status: checkPlayListExist.playlist_status,
          } as any);
        }
      }

      await PlaylistCategory.setHeaders(req).update(
        {
          updated_by: req.user.id,
          playlist_category_status: playlist_category_status.INACTIVE,
        },
        {
          where: {
            category_id: { [Op.notIn]: category_id },
            playlist_id: checkPlayListExist.id,
          },
        },
      );

      for (let i = 0; branch_id.length > i; i++) {
        const findPlaylistBranch = await PlaylistBranch.findOne({
          where: {
            branch_id: branch_id[i],
            playlist_id: checkPlayListExist.id,
          },
        });
        if (findPlaylistBranch) {
          await PlaylistBranch.setHeaders(req).update(
            {
              updated_by: req.user.id,
              playlist_branch_status: checkPlayListExist.playlist_status,
            },
            {
              where: {
                branch_id: findPlaylistBranch.branch_id,
                playlist_id: findPlaylistBranch.playlist_id,
              },
            },
          );
        } else {
          await PlaylistBranch.setHeaders(req).create({
            branch_id: branch_id[i],
            playlist_id: checkPlayListExist.id,
            created_by: req.user.id,
            updated_by: req.user.id,
            playlist_branch_status: checkPlayListExist.playlist_status,
          } as any);
        }
      }

      await PlaylistBranch.setHeaders(req).update(
        {
          updated_by: req.user.id,
          playlist_branch_status: playlist_branch_status.INACTIVE,
        },
        {
          where: {
            branch_id: { [Op.notIn]: branch_id },
            playlist_id: checkPlayListExist.id,
          },
        },
      );

      for (let i = 0; department_id.length > i; i++) {
        const findPlaylistDepartment = await PlaylistDepartment.findOne({
          where: {
            department_id: department_id[i],
            playlist_id: checkPlayListExist.id,
          },
        });
        if (findPlaylistDepartment) {
          await PlaylistDepartment.setHeaders(req).update(
            {
              updated_by: req.user.id,
              playlist_department_status: checkPlayListExist.playlist_status,
            },
            {
              where: {
                department_id: findPlaylistDepartment.department_id,
                playlist_id: findPlaylistDepartment.playlist_id,
              },
            },
          );
        } else {
          await PlaylistDepartment.setHeaders(req).create({
            department_id: department_id[i],
            playlist_id: checkPlayListExist.id,
            created_by: req.user.id,
            updated_by: req.user.id,
            playlist_department_status: checkPlayListExist.playlist_status,
          } as any);
        }
      }

      await PlaylistDepartment.setHeaders(req).update(
        {
          updated_by: req.user.id,
          playlist_department_status: playlist_department_status.INACTIVE,
        },
        {
          where: {
            department_id: { [Op.notIn]: department_id },
            playlist_id: checkPlayListExist.id,
          },
        },
      );
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_PLAYLIST_UPDATING") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("FAIL_PLAYLIST_UPDATING") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete playlist
 * @param req
 * @param res
 * @returns
 */

const deletePlaylist = async (req: any, res: any) => {
  try {
    const { playlist_id } = req.params;
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const findPlaylistExist = await Playlist.findOne({
      where: {
        id: playlist_id,
        playlist_status: { [Op.not]: playlist_status.DELETED },
      },
    });

    if (findPlaylistExist) {
      const findMediaExist = await PlaylistMedia.findOne({
        where: {
          playlist_id: findPlaylistExist?.id,
          playlist_media_status: playlist_media_status.ACTIVE,
        },
      });

      if (findMediaExist) {
        return res
          .status(StatusCodes.OK)
          .json({ status: true, message: res.__("FAIL_MEDIA_ALREADY_EXIST") });
      }

      const updateExistingPlaylist = await Playlist.setHeaders(req).update(
        { playlist_status: playlist_status.DELETED, updated_by: req.user.id },
        { where: { id: findPlaylistExist.id } },
      );

      if (updateExistingPlaylist.length) {
        // Delete playlist image from S3 bucket if exists
        if (findPlaylistExist.playlist_image) {
          const item = await Item.findOne({
            where: { id: findPlaylistExist.playlist_image },
          });

          if (item) {
            await deleteFileFromBucket(
              process.env.NODE_ENV || "development",
              item.item_location,
            );
            await Item.update(
              { item_status: item_status.DELETED },
              { where: { id: item.id } },
            );
          }
        }

        await PlaylistCategory.setHeaders(req).update(
          {
            updated_by: req.user.id,
            playlist_category_status: playlist_category_status.DELETED,
          },
          { where: { playlist_id: findPlaylistExist.id } },
        );

        await PlaylistBranch.setHeaders(req).update(
          {
            updated_by: req.user.id,
            playlist_branch_status: playlist_branch_status.DELETED,
          },
          { where: { playlist_id: findPlaylistExist.id } },
        );

        await PlaylistDepartment.setHeaders(req).update(
          {
            updated_by: req.user.id,
            playlist_department_status: playlist_department_status.DELETED,
          },
          { where: { playlist_id: findPlaylistExist.id } },
        );

        return res
          .status(StatusCodes.OK)
          .json({ status: true, message: res.__("SUCCESS_PLAYLIST_DELETED") });
      } else {
        return res
          .status(StatusCodes.BAD_REQUEST)
          .json({ status: true, message: res.__("FAIL_PLAYLIST_DELETATION") });
      }
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete playlist image
 * @param req
 * @param res
 * @returns
 */

const deletePlaylistImage = async (req: any, res: any) => {
  try {
    const { playlist_id } = req.params;

    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const findPlaylistImage: any = await Playlist.findOne({
      where: {
        id: playlist_id,
        playlist_status: { [Op.not]: playlist_status.DELETED },
      },
      nest: true,
      raw: true,
    });
    if (!findPlaylistImage) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("FILE_NOT_FOUND"),
      });
    }

    if (findPlaylistImage.playlist_image) {
      // Delete file from S3 bucket
      const item = await Item.findOne({
        where: { id: findPlaylistImage.playlist_image },
      });

      if (item) {
        await deleteFileFromBucket(
          process.env.NODE_ENV || "development",
          item.item_location,
        );
        await Item.update(
          { item_status: item_status.DELETED },
          { where: { id: item.id } },
        );

        await Playlist.setHeaders(req).update(
          {
            playlist_image: "null",
            playlist_name: findPlaylistImage.playlist_name,
            playlist_description: findPlaylistImage.playlist_description,
            playlist_status: findPlaylistImage.playlist_status,
            updated_by: req.user.id,
          },
          { where: { id: findPlaylistImage.id } },
        );

        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("SUCCESS_FILE_DELETED"),
        });
      }
    }

    // File does not exist
    return res.status(StatusCodes.NOT_FOUND).json({
      status: false,
      message: res.__("FILE_NOT_FOUND"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get playlist
 * @param req
 * @param res
 * @returns
 */

const getPlaylist = async (req: any, res: any) => {
  try {
    const { category_id, search, playlistStatus } = req.query;
    const { size, page } = req.query;

    const { limit, offset } = getPagination(Number(page), Number(size));

    const whereObj: any = {};
    if (playlistStatus == playlist_status.ACTIVE) {
      whereObj.playlist_status = playlist_status.ACTIVE;
    } else {
      whereObj.playlist_status = { [Op.not]: playlist_status.DELETED };
    }

    if (category_id) {
      whereObj.id = {
        [Op.in]: [
          sequelize.literal(`(select playlist_id from nv_playlist_category where category_id = ${category_id}
            )`),
        ],
      };
    }
    if (search) {
      whereObj.playlist_name = { [Op.like]: `%${search}%` };
    }

    const getPlaylistObj: any = {
      attributes: [
        "id",
        [
          sequelize.literal(
            `IF(playlist_image IS not NULL AND playlist_image != 'null', CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = Playlist.playlist_image)), '')`,
          ),
          "playlist_image_link",
        ],
        "playlist_image",
        [
          sequelize.literal(`(select GROUP_CONCAT(branch_name) from nv_branches where id in (select branch_id from nv_playlist_branch where playlist_id = Playlist.id and playlist_branch_status = '${playlist_branch_status.ACTIVE}' )
      )`),
          "branch",
        ],
        [
          sequelize.literal(`(select GROUP_CONCAT(category_name) from nv_category where id in (select category_id from nv_playlist_category where playlist_id = Playlist.id and playlist_category_status = '${playlist_category_status.ACTIVE}' )
      )`),
          "category",
        ],
        [
          sequelize.literal(`(select GROUP_CONCAT(department_name) from nv_departments where id in (select department_id from nv_playlist_department where playlist_id = Playlist.id and playlist_department_status = '${playlist_department_status.ACTIVE}')
      )`),
          "department",
        ],
        "playlist_name",
        "playlist_description",
        "playlist_status",
      ],
      where: whereObj,
      raw: true,
    };

    if (page && size) {
      getPlaylistObj.limit = Number(limit);
      getPlaylistObj.offset = Number(offset);
    }

    const { rows: findCategoryExist, count } = await Playlist.findAndCountAll(getPlaylistObj);
    const { total_pages } = getPaginatedItems(
      size,
      page,
      count || 0,
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: findCategoryExist,
      total_pages,
      page: parseInt(page),
      size: parseInt(size),
      count: count,
    });

  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get playlist by playlist id
 * @param req
 * @param res
 * @returns
 */

const getPlaylistById = async (req: any, res: any) => {
  try {
    const { playlist_id } = req.params;
    const getPlaylistById: any = await Playlist.findOne({
      attributes: [
        "id",
        [
          sequelize.literal(
            `IF(playlist_image IS not NULL AND playlist_image != 'null', CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = Playlist.playlist_image)), '')`,
          ),
          "playlist_image_link",
        ],
        "playlist_image",
        "playlist_name",
        "playlist_description",
        "playlist_status",
      ],
      where: {
        id: playlist_id,
        playlist_status: { [Op.not]: playlist_status.DELETED },
      },
      raw: true,
    });

    if (getPlaylistById) {
      getPlaylistById.branch = await Branch.findAll({
        attributes: ["id", "branch_name"],
        where: {
          id: {
            [Op.in]: [
              sequelize.literal(
                `select branch_id from nv_playlist_branch where playlist_id = ${getPlaylistById.id} and playlist_branch_status	= '${playlist_branch_status.ACTIVE}'`,
              ),
            ],
          },
        },
      });

      getPlaylistById.department = await Department.findAll({
        attributes: ["id", "department_name"],
        where: {
          id: {
            [Op.in]: [
              sequelize.literal(
                `select department_id from nv_playlist_department where playlist_id = ${getPlaylistById.id} and playlist_department_status = '${playlist_department_status.ACTIVE}'`,
              ),
            ],
          },
        },
      });
      getPlaylistById.category = await Category.findAll({
        attributes: ["id", "category_name"],
        where: {
          id: {
            [Op.in]: [
              sequelize.literal(
                `select category_id from nv_playlist_category where playlist_id = ${getPlaylistById.id} and playlist_category_status ='${playlist_category_status.ACTIVE}' `,
              ),
            ],
          },
        },
      });
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getPlaylistById,
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: true,
        message: res.__("FAIL_DATA_NOT_FOUND"),
        data: [],
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get playlist list by Category id
 * @param req
 * @param res
 * @returns
 */

const getPlaylistByCategory = async (req: any, res: any) => {
  try {
    const { category_id } = req.params;
    const { size, page, view_type } = req.query;

    const checkCategoryExist = await Category.findOne({
      attributes: [
        "id",
        "category_name",
        "category_description",
        "category_status",
        "dashboard_view",
        [
          sequelize.literal(
            `(
            SELECT
            (CASE
              WHEN ((ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))) = 100) THEN 'completed'
              WHEN ((ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))) = 0) THEN 'pending'
              ELSE 'ongoing'
            END)
            FROM nv_playlist_media
            WHERE playlist_media_status = 'active' 
                AND playlist_id IN ( SELECT id FROM nv_playlist WHERE id IN( SELECT
                        playlist_id
                    FROM
                        nv_playlist_category
                    WHERE
                        category_id = Category.id AND playlist_category_status = 'active'
                ) AND id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id =  ${req.user.branch_id} AND playlist_branch_status = 'active') AND 
                id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = ${req.user.department_id} AND playlist_department_status = 'active') 
                AND playlist_status = 'active'
            )
        )`,
          ),
          "category_pl_track_status",
        ],
        [
          sequelize.literal(
            `(
            SELECT
              ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))
            FROM nv_playlist_media
            WHERE playlist_media_status = 'active' 
                AND playlist_id IN ( SELECT id FROM nv_playlist WHERE id IN( SELECT
                        playlist_id
                    FROM
                        nv_playlist_category
                    WHERE
                        category_id = Category.id AND playlist_category_status = 'active'
                ) AND id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id =  ${req.user.branch_id} AND playlist_branch_status = 'active') AND 
                id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = ${req.user.department_id} AND playlist_department_status = 'active') 
                AND playlist_status = 'active'
            )
        )`,
          ),
          "category_pl_track_percentage",
        ],
      ],
      where: { id: category_id, category_status: category_status.ACTIVE },
    });
    if (!checkCategoryExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("CATEGORY_NOT_FOUND") });
    }
    const { limit, offset } = getPagination(Number(page), Number(size));

    const playlistObj: any = {
      attributes: [
        "id",
        [
          sequelize.literal(
            `IF(playlist_image IS not NULL AND playlist_image != 'null', CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = Playlist.playlist_image)), '')`,
          ),
          "playlist_image_link",
        ],
        "playlist_image",
        "playlist_name",
        "playlist_description",
        "playlist_status",
        [
          sequelize.literal(
            `(
              SELECT (CASE
              WHEN (ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*)) = 100) THEN 'completed'
              WHEN (ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*)) = 0) THEN 'pending'
              ELSE 'ongoing'
          END)
          FROM nv_playlist_media
              WHERE playlist_media_status = 'active' 
                  AND playlist_id = Playlist.id
              )`,
          ),
          "playlist_track_status",
        ],
        [
          sequelize.literal(
            `(
              SELECT
                ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))
              FROM nv_playlist_media
              WHERE playlist_media_status = 'active' 
                  AND playlist_id = Playlist.id
          )`,
          ),
          "playlist_track_percentage",
        ],
      ],
      where: {
        id: {
          [Op.in]: [
            sequelize.literal(
              `(select playlist_id from nv_playlist_category where category_id = ${category_id} and playlist_category_status = '${playlist_category_status.ACTIVE}')`,
            ),
          ],
        },
        playlist_status: playlist_status.ACTIVE,
      },
      raw: true,
    };

    const getUserRole: any = await Role.findOne({
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? req.user.web_user_active_role_id
            : req.user.user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (NORMAL_USER.includes(getUserRole?.role_name)) {
      const filterArray = [
        sequelize.literal(
          `(select playlist_id from nv_playlist_category where category_id = ${category_id} and playlist_category_status = '${playlist_category_status.ACTIVE}' AND (select COUNT(*) from nv_playlist_media where playlist_id = nv_playlist_category.playlist_id and playlist_media_status = '${playlist_media_status.ACTIVE}') > 0 AND playlist_id = Playlist.id)`,
        ),

        sequelize.literal(
          `(select playlist_id from nv_playlist_branch where branch_id = ${req.user.branch_id} and playlist_branch_status	 = '${playlist_branch_status.ACTIVE}' AND (select COUNT(*) from nv_playlist_media where playlist_id = nv_playlist_branch.playlist_id and playlist_media_status = '${playlist_media_status.ACTIVE}') > 0 AND playlist_id = Playlist.id)`,
        ),
      ];
      if (view_type == "own" || !view_type) {
        filterArray.push(
          sequelize.literal(
            `(select playlist_id from nv_playlist_department where department_id = ${req.user.department_id} and playlist_department_status = '${playlist_department_status.ACTIVE}' AND (select COUNT(*) from nv_playlist_media where playlist_id = nv_playlist_department.playlist_id and playlist_media_status = '${playlist_media_status.ACTIVE}') > 0 AND playlist_id = Playlist.id)`,
          ),
        );
      }
      playlistObj.where[Op.and] = filterArray;
    }
    if (size && page) {
      playlistObj.limit = Number(limit);
      playlistObj.offset = Number(offset);
    }

    const { rows: getPlaylist, count } =
      await Playlist.findAndCountAll(playlistObj);

    const { total_pages } = getPaginatedItems(size, page, count || 0);

    if (getPlaylist) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getPlaylist,
        category_data: checkCategoryExist,
        total_pages,
        count: count,
        page: parseInt(page),
        size: parseInt(size),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_DATA_FETCHED"),
        data: [],
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Upload Media
 * @param req
 * @param res
 * @returns
 */

const createMedia = async (req: any, res: any) => {
  try {
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }

    const findUserRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res
        .status(StatusCodes.FORBIDDEN)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });
    }

    const { error } = await mediaValidator.createMedia.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const {
      media_title,
      media_description,
      mediaStatus,
      is_external_link,
      media_name,
      media_type,
      is_notify,
      notification_content,
    } = req.body;

    const playlist_id =
      req.body.playlist_id != "null"
        ? req.body.playlist_id.split(",").map(Number)
        : [];

    if (playlist_id.length > 0) {
      const findPlaylistExist = await Playlist.findAll({
        where: {
          id: { [Op.in]: playlist_id },
          playlist_status: playlist_status.ACTIVE,
        },
      });

      if (findPlaylistExist.length != playlist_id.length) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("PLAYLIST_NOT_FOUND") });
      }
    }

    const mediaName =
      is_external_link == "false"
        ? req.file
          ? req.file.filename
          : ""
        : media_name;

    // const media_type =  is_external_link == 'false' ? req.file ? req.file.mimetype.split("/")[0] : "" : 'youtube'
    const addMedia = await Media.setHeaders(req).create({
      is_external_link,
      media_title,
      media_description,
      media_status: mediaStatus,
      media_name: mediaName,
      media_type: media_type,
      created_by: req.user.id,
      updated_by: req.user.id,
    } as any);

    if (addMedia) {
      if (playlist_id.length > 0) {
        for (let i = 0; playlist_id.length > i; i++) {
          await PlaylistMedia.setHeaders(req).create({
            media_id: addMedia.id,
            playlist_id: playlist_id[i],
            playlist_media_status: addMedia.media_status,
            order: i + 1,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);

          if (is_notify == "true") {
            const findUserDetail = await User.findAll({
              where: {
                branch_id: {
                  [Op.in]: [
                    sequelize.literal(
                      `select branch_id from nv_playlist_branch where playlist_id In(playlist_id) and playlist_branch_status = '${playlist_branch_status.ACTIVE}'`,
                    ),
                  ],
                },
                department_id: {
                  [Op.in]: [
                    sequelize.literal(
                      `select department_id from nv_playlist_department where playlist_id In(playlist_id) and playlist_department_status = '${playlist_department_status.ACTIVE}'`,
                    ),
                  ],
                },
              },
              raw: true,
              nest: true,
            });
            if (findUserDetail.length > 0) {
              for (const user of findUserDetail) {
                const findPlaylistCategory = await PlaylistCategory.findAll({
                  where: {
                    playlist_id: playlist_id[i],
                    playlist_category_status: playlist_category_status.ACTIVE,
                  },
                });
                const notificationObj: any = {
                  notification_content: notification_content,
                  from_user_id: req && req.user ? req.user.id : null,
                  notification_status: 'sent',
                  to_user_id: user.id,
                  notification_type: NOTIFICATION_TYPE.INDIVIDUAL,
                  redirection_type: REDIRECTION_TYPE.MEDIA || null,
                  reference_id: addMedia.id || null,
                  created_by: req && req.user ? req.user.id : null,
                  updated_by: req && req.user ? req.user.id : null,
                  notification_subject:
                    NOTIFICATIONCONSTANT.MEDIA_ADDED.heading,
                  redirection_object: {
                    media_id: addMedia.id,
                    playlist_id: playlist_id[i],
                  },
                };
                if (findPlaylistCategory.length > 0) {
                  for (const category of findPlaylistCategory) {
                    notificationObj.redirection_object.category_id =
                      category.category_id;
                  }
                }
                notificationObj.redirection_object = JSON.stringify(
                  notificationObj.redirection_object,
                );
                const deviceId = [];

                if (user.appToken && !user.webAppToken) {
                  deviceId.push(user.appToken);
                } else if (!user.appToken && user.webAppToken) {
                  deviceId.push(user.webAppToken);
                } else if (user.appToken && user.webAppToken) {
                  deviceId.push(user.webAppToken);
                  deviceId.push(user.appToken);
                }
                // const createNotification = await UserNotification.setHeaders(req).create(notificationObj)
                // if (createNotification) {
                //   notificationObj.notification_id = createNotification.id
                // }
                if (deviceId.length > 0) {
                  notificationObj.redirection_object = JSON.parse(
                    notificationObj.redirection_object,
                  );
                  await sendPushNotification(
                    deviceId,
                    notificationObj.notification_content,
                    notificationObj,
                    "",
                    notificationObj.notification_subject,
                  );
                }
              }
            }
          }
        }
      }
      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_MEDIA_UPLOADED") });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("FAIL_MEDIA_UPLOADATION") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Update upload Media
 * @param req
 * @param res
 * @returns
 */

const updateMedia = async (req: any, res: any) => {
  try {
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const { error: fileError } = await mediaValidator.media_upload.validate(
      req.file,
    );

    if (fileError) {
      return res
        .status(400)
        .json({ status: false, message: fileError.details[0].message });
    }

    const { error } = await mediaValidator.updateMedia.validate(req.body);
    if (error) {
      return res
        .status(400)
        .json({ status: false, message: error.details[0].message });
    }
    const {
      media_title,
      media_description,
      media_type,
      mediaStatus,
      is_external_link,
      media_name,
    } = req.body;

    const { media_id } = req.params;
    const playlist_id =
      req.body.playlist_id != "null"
        ? req.body.playlist_id.split(",").map(Number)
        : [];

    if (playlist_id.length > 0) {
      const findPlaylistExist = await Playlist.findAll({
        where: {
          id: { [Op.in]: playlist_id },
          playlist_status: playlist_status.ACTIVE,
        },
      });

      if (findPlaylistExist.length != playlist_id.length) {
        return res
          .status(StatusCodes.EXPECTATION_FAILED)
          .json({ status: false, message: res.__("PLAYLIST_NOT_FOUND") });
      }
    }
    const findMedia = await Media.findOne({
      where: { id: media_id, media_status: { [Op.not]: media_status.DELETED } },
    });
    if (!findMedia) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("MEDIA_NOT_FOUND") });
    }

    // Handle the media file
    let mediaItemId = findMedia.media_name;
    if (is_external_link === "false" && req.files && req.files.length > 0) {
      mediaItemId = req.files[0].item_id;
      // Move file to proper location if needed
      if (req.files[0].isMovable) {
        await moveFileInBucket(
          req.files[0].bucket,
          req.files[0].path,
          FILE_UPLOAD_CONSTANT.MEDIA_FILES.destinationPath(
            req.user.organization_id,
            req.files[0].filename,
          ),
          req.files[0].item_id,
          true,
        );
      }
    }

    const addUploadMedia = await Media.setHeaders(req).update(
      {
        is_external_link,
        media_title,
        media_description,
        media_status: mediaStatus,
        media_name: is_external_link === "true" ? media_name : mediaItemId,
        media_type: media_type,
        updated_by: req.user.id,
      },
      { where: { id: findMedia.id } },
    );

    if (addUploadMedia.length > 0) {
      if (playlist_id.length > 0) {
        for (let i = 0; playlist_id.length > i; i++) {
          const findExistPlaylistMedia = await PlaylistMedia.findOne({
            where: { playlist_id: playlist_id[i], media_id: findMedia.id },
          });

          if (findExistPlaylistMedia) {
            await PlaylistMedia.setHeaders(req).update(
              {
                playlist_media_status: findMedia.media_status,
                updated_by: req.user.id,
              },
              {
                where: {
                  playlist_id: findExistPlaylistMedia.playlist_id,
                  media_id: findExistPlaylistMedia.media_id,
                },
              },
            );
          } else {
            await PlaylistMedia.setHeaders(req).create({
              media_id: findMedia.id,
              playlist_id: playlist_id[i],
              playlist_media_status: findMedia.media_status,
              order: i + 1,
              created_by: req.user.id,
              updated_by: req.user.id,
            } as any);
          }
        }
        await PlaylistMedia.setHeaders(req).update(
          {
            playlist_media_status: media_status.INACTIVE,
            updated_by: req.user.id,
          },
          {
            where: {
              playlist_id: { [Op.notIn]: playlist_id },
              media_id: findMedia.id,
            },
          },
        );
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_MEDIA_UPDATE_UPLOADED"),
      });
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_MEDIA_UPLOADATION") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete media
 * @param req
 * @param res
 * @returns
 */

const deleteMedia = async (req: any, res: any) => {
  try {
    const { media_id } = req.params;

    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const findMedia: any = await Media.findOne({
      where: { id: media_id, media_status: { [Op.not]: media_status.DELETED } },
      nest: true,
      raw: true,
    });
    if (!findMedia) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("FILE_NOT_FOUND"),
      });
    }

    const deleteUploadMedia = await Media.setHeaders(req).update(
      { media_status: media_status.DELETED, updated_by: req.user.id },
      { where: { id: findMedia.id } },
    );

    if (deleteUploadMedia.length > 0) {
      await PlaylistMedia.setHeaders(req).update(
        {
          playlist_media_status: playlist_media_status.DELETED,
          updated_by: req.user.id,
        },
        { where: { media_id: findMedia.id } },
      );

      if (findMedia.is_external_link !== "true" && findMedia.media_name) {
        // Delete file from S3 bucket
        const item = await Item.findOne({
          where: { id: findMedia.media_name },
        });

        if (item) {
          await deleteFileFromBucket(
            process.env.NODE_ENV || "development",
            item.item_location,
          );
          await Item.update(
            { item_status: item_status.DELETED },
            { where: { id: item.id } },
          );
        }
      }

      return res
        .status(StatusCodes.OK)
        .json({ status: true, message: res.__("SUCCESS_MEDIA_DELETED") });
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_MEDIA_DELETATION") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Delete media file
 * @param req
 * @param res
 * @returns
 */

const deleteMediaFile = async (req: any, res: any) => {
  try {
    const { media_id } = req.params;

    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const findMediaFile: any = await Media.findOne({
      where: { id: media_id, media_status: { [Op.not]: media_status.DELETED } },
      nest: true,
      raw: true,
    });
    if (!findMediaFile) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("FILE_NOT_FOUND"),
      });
    }

    if (findMediaFile.is_external_link !== "true" && findMediaFile.media_name) {
      // Delete file from S3 bucket
      const item = await Item.findOne({
        where: { id: findMediaFile.media_name },
      });

      if (item) {
        await deleteFileFromBucket(
          process.env.NODE_ENV || "development",
          item.item_location,
        );
        await Item.update(
          { item_status: item_status.DELETED },
          { where: { id: item.id } },
        );

        await Media.setHeaders(req).update(
          {
            media_name: "null",
            media_type: "null",
            media_description: findMediaFile.media_description,
            media_status: findMediaFile.media_status,
            media_title: findMediaFile.media_title,
            updated_by: req.user.id,
          },
          { where: { id: media_id } },
        );

        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("SUCCESS_FILE_DELETED"),
        });
      }
    }

    // File does not exist or is external link
    return res.status(StatusCodes.NOT_FOUND).json({
      status: false,
      message: res.__("FILE_NOT_FOUND"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get media file list
 * @param req
 * @param res
 * @returns
 */

const getMediaFile = async (req: any, res: any) => {
  try {
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id:
          req.headers["platform-type"] == "web"
            ? getUserDetail.web_user_active_role_id
            : getUserDetail.user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const { search, status } = req.query;
    let { size, page } = req.query;
    if (page && size) {
      page = parseInt(page) || 1;
      size = parseInt(size) || 10;
    }
    const { limit, offset } = getPagination(page, size);
    const whereObj: any = { media_status: { [Op.not]: media_status.DELETED } };
    if (search) {
      whereObj.media_title = { [Op.like]: `%${search}%` };
    }

    if (status) {
      whereObj.media_status = status;
    }
    const mediaListObj: any = {
      where: whereObj,
      attributes: [
        "id",
        "media_description",
        "media_status",
        "media_title",
        "media_type",
        [
          sequelize.literal(
            `IF(is_external_link = 1 OR media_name IS NULL, media_name, CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = Media.media_name)))`,
          ),
          "media_link",
        ],
        [
          sequelize.literal(
            `(SELECT GROUP_CONCAT(pm.playlist_id) 
            FROM nv_playlist_media AS pm 
            JOIN nv_playlist AS p ON p.id = pm.playlist_id 
            WHERE pm.media_id = Media.id AND pm.playlist_media_status	= Media.media_status AND pm.playlist_media_status = 'active' AND p.playlist_status = 'active')`,
          ),
          "checked_playlist",
        ],
        "media_name",
        "is_external_link",
      ],
      nest: true,
      raw: true,
    };

    if (page && size) {
      mediaListObj.limit = Number(limit);
      mediaListObj.offset = Number(offset);
    }
    const mediaList = await Media.findAll(mediaListObj);
    const { total_pages } = getPaginatedItems(
      size,
      page,
      mediaList.length || 0,
    );
    const count = await Media.count({ where: whereObj });
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: mediaList,
      page: parseInt(page),
      size: parseInt(size),
      count,
      total_pages,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get media file by media id
 * @param req
 * @param res
 * @returns
 */

const getMediaById = async (req: any, res: any) => {
  try {
    const { media_id } = req.params;
    const getMedia = await Media.findOne({
      where: { id: media_id, media_status: { [Op.not]: media_status.DELETED } },
      attributes: [
        "id",
        "media_description",
        "media_status",
        "media_title",
        "media_type",
        "is_external_link",
        [
          sequelize.literal(
            `IF(is_external_link = 1 OR media_name IS NULL, media_name, CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = Media.media_name)))`,
          ),
          "media_link",
        ],
        [
          sequelize.literal(
            `(select GROUP_CONCAT(playlist_id) from nv_playlist_media where media_id = Media.id AND playlist_media_status	= Media.media_status )`,
          ),
          "checked_playlist",
        ],
        "media_name",
      ],
      nest: true,
      raw: true,
    });

    if (getMedia) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getMedia,
      });
    } else {
      return res
        .status(StatusCodes.BAD_REQUEST)
        .json({ status: false, message: res.__("FAIL_DATA_FETCHING") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get media file by playlist id
 * @param req
 * @param res
 * @returns
 */

const getMediaByPlaylist = async (req: any, res: any) => {
  try {
    const { playlist_id } = req.params;
    const { size, page, search } = req.query;

    const { limit, offset } = getPagination(Number(page), Number(size));

    const whereObj: any = {
      id: {
        [Op.in]: [
          sequelize.literal(`(select media_id from nv_playlist_media where playlist_id = ${playlist_id} and playlist_media_status = '${playlist_media_status.ACTIVE}'
        )`),
        ],
      },
      media_status: {
        [Op.not]: media_status.DELETED,
      },
    };

    if (req.headers["platform-type"] != "web") {
      whereObj.media_status = media_status.ACTIVE;
    }
    if (search) {
      whereObj.media_title = { [Op.like]: `%${search}%` };
    }

    const getMediaObj: any = {
      attributes: [
        "id",
        [
          sequelize.literal(
            `IF(is_external_link = 1 OR media_name IS NULL, media_name, CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = Media.media_name)))`,
          ),
          "media_link",
        ],
        [
          sequelize.literal(
            `(select playlist_media_track_status from nv_playlist_media_track where playlist_id = ${playlist_id} and media_id = Media.id and user_id = ${req.user.id})`,
          ),
          "media_track_status",
        ],
        [
          sequelize.literal(
            `(select nv_playlist_media.order from nv_playlist_media where playlist_id = ${playlist_id} and media_id = Media.id and playlist_media_status = '${playlist_media_status.ACTIVE}')`,
          ),
          "media_order",
        ],
        "media_name",
        "media_title",
        "is_external_link",
        "media_status",
        "media_type",
        "media_description",
      ],
      where: whereObj,
      raw: true,
    };

    if (page && size) {
      (getMediaObj.limit = Number(limit)),
        (getMediaObj.offset = Number(offset));
    }

    const { rows: getMedia, count } = await Media.findAndCountAll(getMediaObj);
    const { total_pages } = getPaginatedItems(
      limit,
      offset,
      count || 0,
    );
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_FETCHED"),
      data: getMedia,
      total_pages,
      page: Number(offset),
      size: Number(limit),
      count: count
    });

  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  add media file into playlist
 * @param req
 * @param res
 * @returns
 */

const addMediaIntoPlaylist = async (req: any, res: any) => {
  try {
    const { playlist_id = [], media_id } = req.body;
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    const findPlayListExist = await Playlist.findAll({
      where: {
        id: { [Op.in]: playlist_id },
        playlist_status: playlist_status.ACTIVE,
      },
    });
    if (findPlayListExist.length != playlist_id.length) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_PLAYLIST_NOT_ACTIVE") });
    }

    const findMediaExist = await Media.findOne({
      where: { id: media_id, media_status: media_status.ACTIVE },
    });
    if (!findMediaExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_MEDIA_NOT_ACTIVE") });
    }

    if (playlist_id.length > 0) {
      for (let i = 0; playlist_id.length > i; i++) {
        const findExistPlaylistMedia = await PlaylistMedia.findOne({
          where: { playlist_id: playlist_id[i], media_id: findMediaExist.id },
        });

        if (findExistPlaylistMedia) {
          await PlaylistMedia.setHeaders(req).update(
            {
              playlist_media_status: findMediaExist.media_status,
              updated_by: req.user.id,
            },
            {
              where: {
                playlist_id: findExistPlaylistMedia.playlist_id,
                media_id: findExistPlaylistMedia.media_id,
              },
            },
          );
        } else {
          await PlaylistMedia.setHeaders(req).create({
            media_id: findMediaExist.id,
            playlist_id: playlist_id[i],
            playlist_media_status: findMediaExist.media_status,
            order: i + 1,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);
        }
      }
      await PlaylistMedia.setHeaders(req).update(
        {
          playlist_media_status: media_status.INACTIVE,
          updated_by: req.user.id,
        },
        {
          where: {
            playlist_id: { [Op.notIn]: playlist_id },
            media_id: findMediaExist.id,
          },
        },
      );
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_MEDIA_ADDED_INTO_PLAYLIST"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_MEDIA_NOT_ADD_INTO_PLAYLIST"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  add playlist  into Category
 * @param req
 * @param res
 * @returns
 */

const addPlaylistIntoCategory = async (req: any, res: any) => {
  try {
    const { playlist_id, category_id } = req.body;
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const findPlayListExist = await Playlist.findOne({
      where: { id: playlist_id, playlist_status: playlist_status.ACTIVE },
    });
    if (!findPlayListExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_PLAYLIST_NOT_ACTIVE") });
    }

    const findCheckListExist = await Category.findOne({
      where: { id: category_id, category_status: category_status.ACTIVE },
    });
    if (!findCheckListExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_CATEGORY_NOT_ACTIVE") });
    }

    const checkAlreadyExist = await PlaylistCategory.findOne({
      where: {
        category_id: findCheckListExist.id,
        playlist_id: findPlayListExist.id,
        playlist_category_status: {
          [Op.not]: playlist_category_status.DELETED,
        },
      },
    });

    if (checkAlreadyExist) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_PLAYLIST_ALREADY_IN_CATEGORY"),
      });
    }
    const AddPlaylistIntoCategory = await PlaylistCategory.setHeaders(
      req,
    ).create({
      category_id: findCheckListExist.id,
      playlist_id: findPlayListExist.id,
      playlist_category_status: playlist_category_status.ACTIVE,
      created_by: req.user.id,
      updated_by: req.user.id,
    } as any);

    if (AddPlaylistIntoCategory) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_PLAYLIST_ADDED_INTO_CATEGORY"),
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_MEDIA_NOT_ADD_INTO_PLAYLIST"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  remove media from playlist
 * @param req
 * @param res
 * @returns
 */

const removeMediaFromPlaylist = async (req: any, res: any) => {
  try {
    const { playlist_id, media_id } = req.query;
    const getUserDetail: any = await User.findOne({
      where: { id: req.user.id },
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const getUserCurrentRole: any = await Role.findOne({
      where: {
        id: getUserDetail.web_user_active_role_id,
        role_status: role_status.ACTIVE,
      },
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(getUserCurrentRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const findPlayListExist = await Playlist.findOne({
      where: { id: playlist_id, playlist_status: playlist_status.ACTIVE },
    });
    if (!findPlayListExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_PLAYLIST_NOT_ACTIVE") });
    }

    const findMediaExist = await Media.findOne({
      where: { id: media_id, media_status: media_status.ACTIVE },
    });
    if (!findMediaExist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("FAIL_MEDIA_NOT_ACTIVE") });
    }

    const findMediaInPlaylist = await PlaylistMedia.findOne({
      where: { playlist_id: playlist_id, media_id: media_id },
    });

    if (findMediaInPlaylist) {
      const removeMedia = await PlaylistMedia.setHeaders(req).update(
        { playlist_media_status: playlist_media_status.DELETED },
        { where: { playlist_id: playlist_id, media_id: media_id } },
      );
      if (removeMedia.length > 0) {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("SUCCESS_MEDIA_REMOVED_FROM_PLAYLIST"),
        });
      } else {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("FAIL_MEDIA_REMOVED_FROM_PLAYLIST"),
        });
      }
    } else {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("FAIL_MEDIA_NOT_FOUND_IN_PLAYLIST"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  change playlist media order
 * @param req
 * @param res
 * @returns
 */

const changePlaylistMediaOrder = async (req: any, res: any) => {
  try {
    const { media_list } = req.body;
    const { playlist_id } = req.params;

    const findPlaylist = await Playlist.findOne({
      where: {
        id: playlist_id,
        playlist_status: { [Op.not]: playlist_status.DELETED },
      },
    });
    if (!findPlaylist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_PLAYLIST_NOT_FOUND") });
    }

    for (let i = 0; media_list.length > i; i++) {
      const findMedia = await Media.findOne({
        where: { id: media_list[i], media_status: media_status.ACTIVE },
      });

      if (findMedia) {
        await PlaylistMedia.setHeaders(req).update(
          {
            order: i + 1,
            updated_by: req.user.id,
          },
          {
            where: {
              playlist_id: playlist_id,
              media_id: media_list[i],
            },
          },
        );
      }
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_PLAYLIST_MEDIA_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  update playlist media track
 * @param req
 * @param res
 * @returns
 */

const updatePlaylistMediaTrack = async (req: any, res: any) => {
  try {
    const { media_list, status } = req.body;
    const { playlist_id } = req.params;

    const findPlaylist = await Playlist.findOne({
      where: {
        id: playlist_id,
        playlist_status: { [Op.not]: playlist_status.DELETED },
      },
    });
    if (!findPlaylist) {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("ERROR_PLAYLIST_NOT_FOUND") });
    }

    for (let i = 0; media_list.length > i; i++) {
      const findMedia = await Media.findOne({
        where: { id: media_list[i], media_status: media_status.ACTIVE },
      });

      if (findMedia) {
        const findPlaylistMediaTrack = await PlaylistMediaTrack.findOne({
          where: {
            playlist_id: playlist_id,
            media_id: media_list[i],
            user_id: req.user.id,
          },
        });
        if (findPlaylistMediaTrack) {
          await PlaylistMediaTrack.setHeaders(req).update(
            {
              playlist_media_track_status: status,
              updated_by: req.user.id,
            },
            {
              where: {
                playlist_id: playlist_id,
                media_id: media_list[i],
                user_id: req.user.id,
              },
            },
          );
        } else {
          await PlaylistMediaTrack.setHeaders(req).create({
            playlist_id,
            media_id: media_list[i],
            playlist_media_track_status: status,
            user_id: req.user.id,
            created_by: req.user.id,
            updated_by: req.user.id,
          } as any);
        }
      }
    }

    const getPlaylistMediaTrackData: any = await PlaylistMediaTrack.findOne({
      attributes: [
        [
          sequelize.literal(
            `(ROUND((COUNT(*)*100)/(SELECT COUNT(*) FROM nv_playlist_media WHERE playlist_id = ${playlist_id} AND playlist_media_status = '${playlist_media_status.ACTIVE}')))`,
          ),
          "percentage",
        ],
      ],
      where: {
        playlist_id: playlist_id,
        user_id: req.user.id,
        playlist_media_track_status: playlist_media_track_status.COMPLETED,
      },
      raw: true,
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_PLAYLIST_MEDIA_TRACK_UPDATED"),
      progress: getPlaylistMediaTrackData
        ? getPlaylistMediaTrackData.percentage
        : 0,
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  get playlist media track data
 * @param req
 * @param res
 * @returns
 */

const getPlaylistMediaTrackData = async (req: any, res: any) => {
  try {
    const { playlist_id } = req.params;
    const getPlaylistMediaTrackData: any = await PlaylistMedia.findOne({
      attributes: [
        [
          sequelize.literal(
            `(
                ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = PlaylistMedia.playlist_id AND media_id = PlaylistMedia.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))
          )`,
          ),
          "percentage",
        ],
      ],
      where: {
        playlist_id: playlist_id,
        playlist_media_status: playlist_media_status.ACTIVE,
      },
      raw: true,
    });

    if (getPlaylistMediaTrackData) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getPlaylistMediaTrackData,
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: true,
        message: res.__("FAIL_DATA_NOT_FOUND"),
        data: [],
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryList,
  createMedia,
  updateMedia,
  deleteMediaFile,
  getMediaFile,
  getPlaylistByCategory,
  getPlaylist,
  createPlaylist,
  getPlaylistById,
  getMediaById,
  updatePlaylist,
  addMediaIntoPlaylist,
  addPlaylistIntoCategory,
  deletePlaylist,
  deleteMedia,
  deletePlaylistImage,
  getMediaByPlaylist,
  changePlaylistMediaOrder,
  updatePlaylistMediaTrack,
  getPlaylistMediaTrackData,
  removeMediaFromPlaylist,
};

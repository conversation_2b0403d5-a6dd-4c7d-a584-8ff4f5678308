import express, { Express } from "express";
import dotenv from "dotenv";
import http from "http";
import cors from "cors";
import bodyParser from "body-parser";
import morgan from "morgan";
import i18n from "./helper/i18n";
import cookieParser from "cookie-parser";
import Handlebars from 'handlebars'
dotenv.config();
const env = process.env.NEXT_NODE_ENV || "development";

import config from "./config/config.json";
import dbconfig from "./config/db.json";
console.log("Env", env);

global.config = JSON.parse(JSON.stringify(config))[env];
global.db = JSON.parse(JSON.stringify(dbconfig))[env];
import { db } from "./models/index";
import { publicRoutes, privateRoutes } from "./routes/index";
import HandleErrorMessage from "./middleware/validatorMessage";
import userAuth from "./middleware/auth";
// import sessionValidator from "./middleware/session";
import { updateLeaveCron, onboardingReminder, removeResignedUser, sendDsrReminderAt12AM, sendDsrReminderAt12PMForPreviousDay, sendReminderAtMondayForWsrForLastWeek, sendReminderAtTuesdayForWsrForLastWeek, sendReminderForLastMonthOn10Expense, sendReminderForLastMonthOn25Expense, budgetReminder, budgetReminderTillDate } from "./helper/cron.service";
import handlebars from "handlebars";
import secureDocs from "./middleware/docSecurity";
import checkAppVersion from "./config/version.policy";
import swaggerUi from 'swagger-ui-express'
import swaggerFile from './docs/swagger.json'
import path from "path"
import { setupConsumers } from "./rabbitmq/consumerQueue";

db.sequelize
  .sync({ alter: true })
  .then(() => {
    console.log("re-sync db.");
  })
  .catch((error: Error) => {
    console.log("DB Error", error);
    throw error;
  });

const app: Express = express();
const router = express.Router();

app.use(morgan("combined"));

router.use(
  cors({
    origin: ["http://localhost:3000", "http://127.0.0.1"],
    credentials: true,
    exposedHeaders: ["set-cookie"],
  }),
);


Handlebars.registerHelper('eqmark', function (this: any, a: any, b: any, options: Handlebars.HelperOptions) {
  if (a == b) {
    return options.fn(this);
  } else {
    return options.inverse(this);
  }
});
Handlebars.registerHelper('styleByIndex', function (this: any, a: any, b: any /** , options: Handlebars.HelperOptions*/) {
  if (a == b) {
    return new Handlebars.SafeString(`style="padding:3px 6px 0px 8px;"`);
  }
});

// Register custom helpers
handlebars.registerHelper('eq', function (a, b) {
  return a === b;
});
// Register helpers
Handlebars.registerHelper('formatCurrency', (value) => {
  return value ? parseFloat(value).toFixed(2) : '0.00';
});


handlebars.registerHelper('lookup', function (obj, field) {
  return obj[field];
});

handlebars.registerHelper('length', function (obj) {
  return Object.keys(obj).length;
});

handlebars.registerHelper('sub', function (a, b) {
  return a - b;
});

Handlebars.registerHelper('formatNumber', function (value) {
  if (typeof value === 'number') {
    return value.toLocaleString();
  }
  return value;
});
Handlebars.registerHelper('isNumber', function (value) {
  return typeof value === 'number';
});

// Define the helper
Handlebars.registerHelper('some', function (this: any, array: any[], key: string, options: any) {
  if (array.some(item => item[key])) {
    return options.fn ? options.fn(this) : '';
  }
  return options.inverse ? options.inverse(this) : '';
});



router.use(bodyParser.urlencoded({ extended: true }));
router.use(bodyParser.json());

router.use(cookieParser());
router.use(i18n.init);
// Dynamically add required fields to Swagger JSON
const swaggerDocument = {
  "swagger": "2.0",
  "info": {
    "title": "Namaste Village API",
    "version": "0.1.0",
    "description": "Welcome to the **Namaste Village** API documentation!\n\n## Overview\nThis API is part of the Namaste Village project, which includes a client-side (frontend) built using Next.js and a server-side (backend) using Express.js.\n\n### Key Directories:\n- **client/**: Frontend components, styles, and utilities.\n- **server/**: Backend API server configuration using Express.js.",
    "contact": {
      "name": "Namaste Village Team",
      "url": "https://namastevillage.teamtrainhub.com"
    }
  },
  "securityDefinitions": {
    "BearerAuth": {
      "type": "apiKey",
      "name": "Authorization",
      "in": "header",
      "description": "Enter your Bearer token in the format `Bearer <token>`"
    }
  },
  "host": `${global.config.SWAGGER_BASE_URL}`,
  "basePath": "/v1",
  "components": {
    "responses": {
      "SuccessResponse": {
        "description": "Successful response",
        "content": {
          "application/json": {
            "schema": {
              "type": "object",
              "properties": {
                "status": {
                  "type": "string",
                  "example": "success",
                  "description": "Indicates the response status"
                },
                "message": {
                  "type": "string",
                  "example": "Request completed successfully.",
                  "description": "A message confirming the request was processed"
                },
                "data": {
                  "type": "object",
                  "additionalProperties": true,
                  "description": "The main data payload"
                }
              }
            }
          }
        }
      },
      "BadRequestResponse": {
        "description": "Bad Request - Invalid request parameters or payload",
        "content": {
          "application/json": {
            "schema": {
              "type": "object",
              "properties": {
                "status": {
                  "type": "string",
                  "example": "error",
                  "description": "Indicates an error in the request"
                },
                "message": {
                  "type": "string",
                  "example": "Invalid request or missing parameters.",
                  "description": "Details about why the request failed"
                }
              }
            }
          }
        }
      },
      "InternalServerErrorResponse": {
        "description": "Internal Server Error - An unexpected server-side error occurred",
        "content": {
          "application/json": {
            "schema": {
              "type": "object",
              "properties": {
                "status": {
                  "type": "string",
                  "example": "error",
                  "description": "Indicates a server-side error"
                },
                "message": {
                  "type": "string",
                  "example": "An internal server error has occurred.",
                  "description": "Details about the server-side issue"
                }
              }
            }
          }
        }
      }
    }
  },
  ...swaggerFile // Merge paths and definitions from the paths-only JSON file
};


// Serve the custom CSS file
router.use('/swagger-ui.css', express.static(path.join(__dirname, 'docs', 'swagger-custom.css')));
// Serve Swagger documentation
router.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument, {
  customCssUrl: '/swagger-ui.css',
  customSiteTitle: "My API Documentation"
}));

app.use((req, res, next) => {
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.header(
    "Access-Control-Expose-Headers",
    "Download-File, Set-Cookie, set-cookie, secure_session, App-Version , Maintenance-Mode",
  );
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization, Set-Cookie, Platform-Type, Ip-Address, Address, Location, App-Version,Maintenance-Mode",
  );
  next();
});

// use route for files 
router.all("/files/*", secureDocs);
router.use("/files", express.static(__dirname + "/uploads"));

router.use("/uploads", express.static(__dirname + "/uploads"));


// /** Use routers *
router.all("*", checkAppVersion)
router.all("/v1/private/*", userAuth);
router.use("/v1/private", privateRoutes);
router.use("/v1/public", publicRoutes);
app.set("trust proxy", true);

router.use('*', (req, res, next) => {
  console.log(
    `[Server][${new Date().toISOString()}] ${req?.method} - ${req?.url} :: ${req?.headers["user-agent"]}`,
  );
  next()
})

if (process.env.NEXT_NODE_ENV !== "staging") {
  onboardingReminder.start();
  removeResignedUser.start();
  sendDsrReminderAt12AM.start();
  sendDsrReminderAt12PMForPreviousDay.start();
  sendReminderAtMondayForWsrForLastWeek.start();
  sendReminderAtTuesdayForWsrForLastWeek.start();
  sendReminderForLastMonthOn10Expense.start();
  sendReminderForLastMonthOn25Expense.start();
} else {
  onboardingReminder.stop();
  removeResignedUser.stop();
  sendDsrReminderAt12AM.stop();
  sendDsrReminderAt12PMForPreviousDay.stop();
  sendReminderAtMondayForWsrForLastWeek.stop();
  sendReminderAtTuesdayForWsrForLastWeek.stop();
  sendReminderForLastMonthOn10Expense.stop();
  sendReminderForLastMonthOn25Expense.stop();
}
updateLeaveCron.start()
if (global.config.BUDGET_REMINDER_CRON) {
  budgetReminder.start()
  budgetReminderTillDate.start()
}

router.get("/", (req, res) => {
  res.send({
    status: true,
    message: "Hello There! i'm a root route of [Namaste Village]",
  });
});

router.get("*", (req, res) => {
  res.status(404).json({
    status: false,
    message: `The requested URL "${req.originalUrl}" was not found on this server`,
  });
});

app.use(router);

// Handle error message
router.use(HandleErrorMessage);

const server = http.createServer(app);
// Initialize database and RabbitMQ consumers
const initializeApp = async () => {
  try {
    /** Listen all queue from subscription-ms */
    // await setupConsumers();

    server.listen(global.config.PORT, () => {
      console.log(`Server is started on`, global.config.PORT);
    });
  } catch (error) {
    console.error("Error during application initialization:", error);
    process.exit(1); // Exit the process if initialization fails
  }
}
// Start application initialization
initializeApp();
export default router;

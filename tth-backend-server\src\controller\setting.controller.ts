import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Setting, setting_status } from "../models/Setting";
// import path from "path";
// import fs from "fs";
import { Branch, branch_status } from "../models/Branch";
import {
  FILE_UPLOAD_CONSTANT,
  ROLE_CONSTANT,
  // URL_CONSTANT,
} from "../helper/constant";
import { Role } from "../models/Role";
import { User, user_status } from "../models/User";
import { Op, QueryTypes } from "sequelize";
import { HealthSafetyCategory } from "../models/HealthSafetyCategory";
import { status } from "../models/RightToWorkCheckList";
// import { playlist_branch_status } from "../models/PlaylistBranch";
import { Playlist } from "../models/Playlist";
import { sequelize } from "../models";
import { HealthSafetyPlaylist } from "../models/HealthSafetyPlaylist";
import { convertFutureLeaves } from "../helper/common";
import {
  general_setting_status,
  GeneralSetting,
} from "../models/GeneralSetting";
import { LeaveAccuralPolicy } from "../models/LeaveAccuralPolicy";
import {
  // deleteFileFromBucket,
  moveFileInBucket,
  // moveFilesLocalToS3,
  s3,
} from "../helper/upload.service";
import {
  Item,
  item_IEC,
  item_status,
  item_type,
  item_external_location,
} from "../models/Item";
import { getHash } from "../helper/common";
import { PutObjectCommand } from "@aws-sdk/client-s3";
import { LeaveTypeModel } from "../models/LeaveType";

const handleEmployerSign = async (
  req: any,
  value: string,
  existingFileName?: string,
): Promise<string> => {
  try {
    const bucketName = process.env.NODE_ENV || "development";
    const fileBuffer = Buffer.from(
      value.split(";base64,").pop() || "",
      "base64",
    );
    const signatureName = existingFileName || `employer_sign_${Date.now()}.png`;
    const filePath = FILE_UPLOAD_CONSTANT.ORG_SETTINGS.destinationPath(
      req.user.organization_id,
      signatureName,
    );

    // Generate hash for deduplication
    const fileHash: any = await getHash(fileBuffer, {
      originalname: signatureName,
      mimetype: "image/png",
    });

    // Check if file already exists by hash
    let fileExists = false;
    let existingItemId = null;
    if (fileHash.status) {
      const getItem: any = await Item.findOne({
        where: {
          item_hash: fileHash.hash,
          item_organization_id: req.user.organization_id,
        },
      });
      if (getItem && getItem.id) {
        fileExists = true;
        existingItemId = getItem.id;
      }
    }

    if (!fileExists) {
      // Upload file to S3
      await s3.send(
        new PutObjectCommand({
          Bucket: bucketName,
          Key: filePath,
          Body: fileBuffer,
          ContentType: "image/png",
        }),
      );

      // Create item record
      const saveItem: any = {
        item_type: item_type.IMAGE,
        item_name: signatureName,
        item_hash: fileHash.hash,
        item_mime_type: "image/png",
        item_extension: ".png",
        item_size: fileBuffer.length,
        item_IEC: item_IEC.B,
        item_status: item_status.ACTIVE,
        item_external_location: item_external_location.NO,
        item_location: filePath,
        item_organization_id: req.user.organization_id,
      };

      const item = await Item.create(saveItem);
      return item.id.toString();
    } else {
      return existingItemId.toString();
    }
  } catch (error) {
    console.error("Error in handleEmployerSign:", error);
    throw error;
  }
};

/**
 * Create Setting
 * @param req
 * @param res
 * @returns
 */

const createSetting = async (req: any, res: any) => {
  try {
    // Handle brand logo upload from multerS3
    req.body.brand_logo =
      req.files && req.files.brand_logo && req.files.brand_logo.length > 0
        ? req.files.brand_logo[0].item_id
        : "";

    if (req.files?.brand_logo?.[0]?.item_id) {
      if (req.files?.brand_logo?.[0]?.isMovable) {
        await moveFileInBucket(
          req.files?.brand_logo?.[0]?.bucket,
          req.files?.brand_logo?.[0]?.path,
          FILE_UPLOAD_CONSTANT.ORG_SETTINGS.destinationPath(
            req.user.organization_id,
            req.files?.brand_logo?.[0]?.filename,
          ),
          req.files?.brand_logo?.[0]?.item_id,
          true,
        );
      }
    }

    let message = ''
    const settingPromises = Object.entries(req.body).map(
      async ([key, value]) => {
        const existingSetting = await Setting.findOne({
          where: { key: key, organization_id: req.user.organization_id },
          raw: true,
        });
        if (existingSetting) {
          // Handle file upload for brand_logo
          value =
            key === "brand_logo" && value === ""
              ? existingSetting.value
              : value;

          if (key === "employer_sign") {
            if (
              value &&
              typeof value === "string" &&
              value.includes("base64")
            ) {
              value = await handleEmployerSign(
                req,
                value as string,
                existingSetting.value,
              );
            } else {
              value = existingSetting.value ? existingSetting.value : "";
            }
          }

          if (key == "leave_period_type") {
            if (existingSetting.value != value) {
              const workingHours = req.body.working_hours_per_day;
              if (req.body.leave_modify_options) {
                await convertFutureLeaves(
                  req.user.id,
                  req.body.leave_modify_options,
                  workingHours,
                  req.user.organization_id,
                );
              }

              const findOrgleaveType = await LeaveTypeModel.findAll({ where: { organization_id: req.user.organization_id, status: status.ACTIVE } })
              if (findOrgleaveType.length > 0) {
                const getleavePolicyList = await LeaveAccuralPolicy.findAll({
                  where: { status: status.ACTIVE, leave_type_id: findOrgleaveType.map((item: any) => item.id) },
                  raw: true,
                });
                if (getleavePolicyList.length > 0) {
                  // or 'hours' — get from config
                  for (const leavePolicy of getleavePolicyList) {
                    if (!leavePolicy.stop_policy_accural_timewise_value) continue;

                    try {
                      const parsedValue = JSON.parse(
                        leavePolicy.stop_policy_accural_timewise_value,
                      );

                      const updatedArray = parsedValue.map((item: any) => {
                        const updatedDays =
                          value == "day" ? item.day_value : item.hour_value;
                        return {
                          ...item,
                          days: updatedDays,
                        };
                      });

                      await LeaveAccuralPolicy.update(
                        {
                          stop_policy_accural_timewise_value:
                            JSON.stringify(updatedArray),
                        },
                        { where: { id: leavePolicy.id } },
                      );
                    } catch (err) {
                      console.error(
                        `Error parsing policy ID ${leavePolicy.id}:`,
                        err,
                      );
                    }
                  }
                }
              }


            }

            await Setting.setHeaders(req).update(
              { key, value, updated_by: req.user.id } as any,
              { where: { id: existingSetting.id } },
            );
          } else {
            await Setting.setHeaders(req).update(
              { key, value, updated_by: req.user.id } as any,
              { where: { id: existingSetting.id } },
            );
          }
          message = res.__("SUCCESS_SETTING_UPDATED")
        } else {
          if (
            key === "employer_sign" &&
            value &&
            typeof value === "string" &&
            value.includes("base64")
          ) {
            value = await handleEmployerSign(req, value as string);
          }

          await Setting.setHeaders(req).create({
            key: key,
            value: value,
            updated_by: req.user.id,
            created_by: req.user.id,
            setting_status: setting_status.ACTIVE,
            organization_id: req.user.organization_id,
          } as any);
          message = res.__("SUCCESS_SETTING_ADDED")
        }
      },
    );

    await Promise.all(settingPromises);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: message
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get Setting
 * @param req
 * @param res
 * @returns
 */

const getSetting = async (req: Request, res: Response) => {
  try {
    const getSettingList = await Setting.findAll({
      where: {
        setting_status: setting_status.ACTIVE,
        organization_id: req.user.organization_id,
      },
      raw: true,
    });

    if (getSettingList.length > 0) {
      const settingObj: any = {};
      for (const key of getSettingList) {
        settingObj[key.key] = key.value;
        if (key.key == "brand_logo") {
          const item = await Item.findOne({
            where: { id: key.value },
            attributes: ["item_location"],
            raw: true,
          });
          settingObj["brand_logo_link"] = item?.item_location
            ? `${global.config.API_BASE_URL}${item.item_location}`
            : "";
        }

        if (key.key == "employer_sign") {
          const item = await Item.findOne({
            where: { id: key.value },
            attributes: ["item_location"],
            raw: true,
          });
          settingObj["employer_sign"] = item?.item_location
            ? `${global.config.API_BASE_URL}${item.item_location}`
            : "";
        }
      }
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_SETTING_LIST_FETCHED"),
        data: settingObj,
      });
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("FAIL_SETTING_NOT_FOUND"),
        data: [],
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const createBranchWiseSetting = async (req: any, res: Response) => {
  try {
    const { employer_sign, employer_name, branch_id, branch_heading_employer_name, branch_heading_name, branch_work_place, branch_heading_work_place } = req.body
    const findBranch = await Branch.findOne({ where: { id: Number(branch_id), branch_status: branch_status.ACTIVE, organization_id: req.user.organization_id }, raw: true })
    if (findBranch) {
      let signatureItemId = findBranch.branch_sign;

      if (
        employer_sign &&
        typeof employer_sign === "string" &&
        employer_sign.includes("base64")
      ) {
        const bucketName = process.env.NODE_ENV || "development";
        const fileBuffer = Buffer.from(
          employer_sign.split(";base64,").pop() || "",
          "base64",
        );
        const signatureName = `branch_signature_${findBranch?.branch_name.trim()}_${findBranch.id}_${Date.now()}.png`;
        const filePath =
          FILE_UPLOAD_CONSTANT.USER_SIGNATURE_PATH.destinationPath(
            req.user.organization_id,
            branch_id,
            signatureName,
          );

        // Generate hash for deduplication
        const fileHash: any = await getHash(fileBuffer, {
          originalname: signatureName,
          mimetype: "image/png",
        });

        // Check if file already exists by hash
        let fileExists = false;
        let existingItemId = null;
        if (fileHash.status) {
          const getItem: any = await Item.findOne({
            where: {
              item_hash: fileHash.hash,
              item_organization_id: req.user.organization_id,
            },
          });
          if (getItem && getItem.id) {
            fileExists = true;
            existingItemId = getItem.id;
          }
        }

        if (!fileExists) {
          // Upload file to S3
          await s3.send(
            new PutObjectCommand({
              Bucket: bucketName,
              Key: filePath,
              Body: fileBuffer,
              ContentType: "image/png",
            }),
          );

          // Create item record
          const saveItem: any = {
            item_type: item_type.IMAGE,
            item_name: signatureName,
            item_hash: fileHash.hash,
            item_mime_type: "image/png",
            item_extension: ".png",
            item_size: fileBuffer.length,
            item_IEC: item_IEC.B,
            item_status: item_status.ACTIVE,
            item_external_location: item_external_location.NO,
            item_location: filePath,
            item_organization_id: req.user.organization_id,
          };

          const item = await Item.create(saveItem);
          signatureItemId = item.id.toString();
        } else {
          signatureItemId = existingItemId.toString();
        }
      }

      const updateBranch = await Branch.setHeaders(req).update(
        {
          branch_sign: signatureItemId,
          branch_employer_name: employer_name,
          branch_heading_employer_name,
          branch_heading_name,
          branch_work_place,
          branch_heading_work_place,
        },
        { where: { id: findBranch.id } },
      );

      if (updateBranch.length > 0) {
        return res.status(StatusCodes.OK).json({
          status: true,
          message: res.__("SUCCESSFULLY_BRANCH_SETTING_ADDED"),
        });
      } else {
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
          status: false,
          message: res.__("FAIL_BRANCH_SETTING"),
        });
      }
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getBranchWiseSetting = async (req: Request, res: Response) => {
  try {
    const { branch_id } = req.params
    const getUserDetail: any = await User.findOne({ where: { id: req.user.id, organization_id: req.user.organization_id, user_status: { [Op.notIn]: [user_status.DELETED, user_status.CANCELLED] } }, raw: true })
    const findUserRole: any = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id }, raw: true
    });

    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }

    let getBranchSetting: any = await Branch.findOne({ attributes: ['id', 'branch_sign', 'branch_employer_name', 'branch_heading_employer_name', 'branch_heading_name', 'branch_work_place', 'branch_heading_work_place'], where: { id: branch_id, branch_status: branch_status.ACTIVE, organization_id: req.user.organization_id }, raw: true })
    if (getBranchSetting) {
      getBranchSetting = JSON.parse(JSON.stringify(getBranchSetting));

      if (getBranchSetting.branch_sign) {
        const item = await Item.findOne({
          where: { id: getBranchSetting.branch_sign },
          attributes: ["item_location"],
          raw: true,
        });

        getBranchSetting.branch_sign = item?.item_location
          ? `${global.config.API_BASE_URL}${item.item_location}`
          : "";
      }

      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_SETTING_LIST_FETCHED"),
        data: getBranchSetting,
      });
    } else {
      return res
        .status(StatusCodes.EXPECTATION_FAILED)
        .json({ status: false, message: res.__("BRANCH_NOT_FOUND") });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getHealthSafetyCategory = async (req: Request, res: Response) => {
  try {
    const { branch_id }: any = req.query
    const getUserDetail: any = await User.findOne({
      attributes: ['id', 'web_user_active_role_id', 'user_active_role_id'],
      where: { id: req.user.id, organization_id: req.user.organization_id, user_status: { [Op.notIn]: [user_status.DELETED, user_status.CANCELLED] } }, raw: true
    })
    const findUserRole: any = await Role.findOne({
      attributes: ['id', 'role_name'],
      where: { id: req.headers["platform-type"] == "web" ? getUserDetail.web_user_active_role_id : getUserDetail.user_active_role_id }, raw: true
    });
    if (
      ![
        ROLE_CONSTANT.SUPER_ADMIN,
        ROLE_CONSTANT.ADMIN,
        ROLE_CONSTANT.DIRECTOR,
        ROLE_CONSTANT.HR,
        ROLE_CONSTANT.BRANCH_MANAGER,
        ROLE_CONSTANT.HOTEL_MANAGER,
      ].includes(findUserRole?.role_name)
    ) {
      return res.status(StatusCodes.EXPECTATION_FAILED).json({
        status: false,
        message: res.__("PERMISSION_DENIED"),
      });
    }
    const getHealthSafetyCategory: any = await HealthSafetyCategory.findAll({
      attributes: ["id", "category_name", "status", "created_by", "updated_by"],
      where: { status: status.ACTIVE },
      raw: true,
      nest: true,
    });
    if (getHealthSafetyCategory.length > 0) {
      for (const category of getHealthSafetyCategory) {
        const getHealthSafetyPlaylist: any = await HealthSafetyPlaylist.findOne(
          {
            include: [
              {
                model: Playlist,
                as: "health_safety_playlist",
                attributes: ["id", "playlist_name"],
              },
            ],
            where: {
              health_safety_category_id: category.id,
              branch_id: branch_id,
              status: status.ACTIVE,
            },
          },
        );
        category.checked_playlist =
          getHealthSafetyPlaylist &&
            getHealthSafetyPlaylist?.health_safety_playlist
            ? getHealthSafetyPlaylist?.health_safety_playlist
            : {};
      }
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getHealthSafetyCategory || [],
      });
    } else {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("FAIL_DATA_NOT_FOUND"),
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

const getBranchSettingUpdateHistory = async (req: Request, res: Response) => {
  try {
    const { branch_id }: any = req.params;

    // Raw SQL query focusing only on specific fields
    const rawQuery = `SELECT
    a.*,
    CONCAT_WS(' ', u.user_first_name, u.user_middle_name, u.user_last_name) AS user_full_name,
    CASE 
            WHEN u.user_avatar IS NULL OR u.user_avatar = '' THEN ''
            WHEN NOT u.user_avatar REGEXP '^[0-9]+$' OR NOT EXISTS (SELECT 1 FROM nv_items WHERE id = u.user_avatar) 
            THEN CONCAT('${global.config.API_BASE_URL}user_avatars/', u.user_avatar)
            ELSE CONCAT('${global.config.API_BASE_URL}', (SELECT item_location FROM nv_items WHERE id = u.user_avatar))
          END AS user_avatar_link
    FROM
        nv_activities a
    JOIN
      nv_users u ON a.created_by = u.id
    WHERE 
    a.activity_table = 'Branch' 
    AND a.reference_id = :branch_id
    AND (
      a.activity_action = 'created' OR
      (
        (CAST(JSON_EXTRACT(a.new_data, '$.branch_employer_name') AS CHAR) != 'null' AND CAST(JSON_EXTRACT(a.previous_data, '$.branch_employer_name') AS CHAR) != CAST(JSON_EXTRACT(a.new_data, '$.branch_employer_name') AS CHAR))
        OR (CAST(JSON_EXTRACT(a.new_data, '$.branch_sign') AS CHAR) != 'null' AND CAST(JSON_EXTRACT(a.previous_data, '$.branch_sign') AS CHAR) != CAST(JSON_EXTRACT(a.new_data, '$.branch_sign') AS CHAR))
        OR (CAST(JSON_EXTRACT(a.new_data, '$.branch_heading_employer_name') AS CHAR) != 'null' AND CAST(JSON_EXTRACT(a.previous_data, '$.branch_heading_employer_name') AS CHAR) != CAST(JSON_EXTRACT(a.new_data, '$.branch_heading_employer_name') AS CHAR))
        OR (CAST(JSON_EXTRACT(a.new_data, '$.branch_heading_name') AS CHAR) != 'null' AND CAST(JSON_EXTRACT(a.previous_data, '$.branch_heading_name') AS CHAR) != CAST(JSON_EXTRACT(a.new_data, '$.branch_heading_name') AS CHAR))
        OR (CAST(JSON_EXTRACT(a.new_data, '$.branch_work_place') AS CHAR) != 'null' AND CAST(JSON_EXTRACT(a.previous_data, '$.branch_work_place') AS CHAR) != CAST(JSON_EXTRACT(a.new_data, '$.branch_work_place') AS CHAR))
        OR (CAST(JSON_EXTRACT(a.new_data, '$.branch_heading_work_place') AS CHAR) != 'null' AND CAST(JSON_EXTRACT(a.previous_data, '$.branch_heading_work_place') AS CHAR) != CAST(JSON_EXTRACT(a.new_data, '$.branch_heading_work_place') AS CHAR))
      )
    )
      ORDER BY a.updatedAt DESC;`;

    // Execute the raw query
    const getBranchSettingDetail = await sequelize.query(rawQuery, {
      replacements: { branch_id },
      type: QueryTypes.SELECT,
    });

    if (getBranchSettingDetail.length > 0) {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_FETCHED"),
        data: getBranchSettingDetail,
      });
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("NO_RECORDS_FOUND"),
        data: [],
      });
    }
  } catch (error) {
    console.log("error", error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 * Create Setting
 * @param req
 * @param res
 * @returns
 */

const createGeneralSetting = async (req: any, res: any) => {
  try {
    const settingPromises = Object.entries(req.body).map(
      async ([key, value]) => {
        const existingSetting = await GeneralSetting.findOne({
          where: { key: key },
        });
        if (existingSetting) {
          // Handle file upload for brand_logo
          await GeneralSetting.setHeaders(req).update(
            { key, value, updated_by: req.user.id } as any,
            { where: { id: existingSetting.id } },
          );
        } else {
          await GeneralSetting.setHeaders(req).create({
            key: key,
            value: value,
            updated_by: req.user.id,
            created_by: req.user.id,
            setting_status: setting_status.ACTIVE,
          } as any);
        }
      },
    );

    await Promise.all(settingPromises);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("SUCCESS_SETTING_ADDED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

/**
 *  Get Setting
 * @param req
 * @param res
 * @returns
 */

const getGeneralSetting = async (req: Request, res: Response) => {
  try {
    const getSettingList = await GeneralSetting.findAll({
      where: { general_setting_status: general_setting_status.ACTIVE },
    });

    if (getSettingList.length > 0) {
      const settingObj: any = {};
      for (const key of getSettingList) {
        settingObj[key.key] = key.value;
      }
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("SUCCESS_SETTING_LIST_FETCHED"),
        data: settingObj,
      });
    } else {
      return res.status(StatusCodes.OK).json({
        status: true,
        message: res.__("FAIL_SETTING_NOT_FOUND"),
        data: [],
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      data: error,
    });
  }
};

export default {
  createSetting,
  getSetting,
  createBranchWiseSetting,
  getBranchWiseSetting,
  getHealthSafetyCategory,
  getBranchSettingUpdateHistory,
  createGeneralSetting,
  getGeneralSetting,
};

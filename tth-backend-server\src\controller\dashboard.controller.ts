import { StatusCodes } from "http-status-codes";
import { Category, category_status } from "../models/Category";
import { getPagination, getPaginatedItems } from "../helper/utils";
import { Department, department_status } from "../models/Department";
import { User, user_status } from "../models/User";
import { sequelize } from "../models";
import { Op } from "sequelize";
import { Media, media_status } from "../models/Media";
import { UserRequest, request_status } from "../models/UserRequest";
import { Resignation, resignation_status } from "../models/Resignation";
import { Branch, branch_status } from "../models/Branch";
import { DocumentCategory } from "../models/DocumentCategory";
import { generateDashboard, generateDateComponents, getDashboardModel, getReportDashboard, getSuperAdminUserId } from "../helper/common";
import { Dashboard, dashboard_status } from "../models/Dashboard";
import moment from "moment";
import { DashboardModel, model_status } from "../models/DashboardModel";
import { getUserDashboardWidgets, getDsrDashboardWidgets, getSetupDashboardWidgets } from "../services/dashboard.service";

/**
 * Get Admin Dashboard Detail
 * @param req
 * @param res
 * @returns
 */

const getAdminDashboardDetail = async (req: any, res: any) => {
    try {
        const excludedUserIds = [];
        const getSuperAdminUser = await getSuperAdminUserId(req.user.organization_id)
        excludedUserIds.push(...getSuperAdminUser)

        const dashboardDetails: any = {};

        // Count active categories
        dashboardDetails.categories = await DocumentCategory.findOne({
            attributes: [
                [sequelize.fn('COUNT', sequelize.col('*')), 'total_categories'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('category_status'), category_status.ACTIVE)), 'total_active'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('category_status'), category_status.DRAFT)), 'total_draft'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('category_status'), category_status.INACTIVE)), 'total_inactive']
            ],
            where: {
                category_status: {
                    [Op.notIn]: [category_status.DELETED]
                }
            }
        });

        // Count active departments
        dashboardDetails.departments = await Department.findOne({
            attributes: [
                [sequelize.fn('COUNT', sequelize.col('*')), 'total_departments'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('department_status'), department_status.ACTIVE)), 'total_active'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('department_status'), department_status.DRAFT)), 'total_draft'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('department_status'), department_status.INACTIVE)), 'total_inactive']
            ],
            where: {
                department_status: {
                    [Op.notIn]: [department_status.DELETED]
                },
                organization_id: req.user.organization_id
            }
        });

        // Count active branches
        dashboardDetails.branches = await Branch.findOne({
            attributes: [
                [sequelize.fn('COUNT', sequelize.col('*')), 'total_branches'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('branch_status'), branch_status.ACTIVE)), 'total_active'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('branch_status'), branch_status.DRAFT)), 'total_draft'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('branch_status'), branch_status.INACTIVE)), 'total_inactive']
            ],
            where: {
                branch_status: {
                    [Op.notIn]: [branch_status.DELETED]
                },
                organization_id: req.user.organization_id
            }
        });

        // Count total admins
        dashboardDetails.admins = await User.findOne({
            attributes: [
                [sequelize.fn('COUNT', sequelize.col('*')), 'total_admins'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.PENDING}' THEN 1 ELSE 0 END`)), 'total_pending'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.ACTIVE}' THEN 1 ELSE 0 END`)), 'total_active'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.ONGOING}' THEN 1 ELSE 0 END`)), 'total_ongoing'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.COMPLETED}' THEN 1 ELSE 0 END`)), 'total_completed'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.VERIFIED}' THEN 1 ELSE 0 END`)), 'total_verified'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.DELETED}' THEN 1 ELSE 0 END`)), 'total_deleted'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.REJECTED}' THEN 1 ELSE 0 END`)), 'total_rejected'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.CANCELLED}' THEN 1 ELSE 0 END`)), 'total_cancelled']
            ],
            where: {
                organization_id: req.user.organization_id,
                id: { [Op.not]: excludedUserIds },
                [Op.and]: [
                    sequelize.literal(`(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id IN (2)))`)
                ]
            },
            raw: true,
            subQuery: false
        });

        dashboardDetails.staffs = await User.findOne({
            attributes: [
                [sequelize.fn('COUNT', sequelize.col('User.id')), 'total_staffs'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.PENDING}' THEN 1 ELSE 0 END`)), 'total_pending'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.ACTIVE}' THEN 1 ELSE 0 END`)), 'total_active'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.ONGOING}' THEN 1 ELSE 0 END`)), 'total_ongoing'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.COMPLETED}' THEN 1 ELSE 0 END`)), 'total_completed'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.VERIFIED}' THEN 1 ELSE 0 END`)), 'total_verified'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.DELETED}' THEN 1 ELSE 0 END`)), 'total_deleted'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.REJECTED}' THEN 1 ELSE 0 END`)), 'total_rejected'],
                [sequelize.fn('SUM', sequelize.literal(`CASE WHEN user_status = '${user_status.CANCELLED}' THEN 1 ELSE 0 END`)), 'total_cancelled']
            ],
            where: {
                organization_id: req.user.organization_id,
                id: { [Op.not]: excludedUserIds },
                [Op.and]: [
                    sequelize.literal(`(( SELECT GROUP_CONCAT(role_id SEPARATOR ",") FROM nv_user_roles WHERE user_id = User.id AND role_id NOT IN (1,2))) `)
                ]
            },
            raw: true,
            subQuery: false
        });


        // Count active media
        dashboardDetails.media = await Media.findOne({
            attributes: [
                [sequelize.fn('COUNT', sequelize.col('*')), 'total_media'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('media_status'), media_status.ACTIVE)), 'total_active'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('media_status'), media_status.DRAFT)), 'total_draft'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('media_status'), media_status.INACTIVE)), 'total_inactive']
            ],
            where: {
                media_status: {
                    [Op.notIn]: [media_status.DELETED]
                }
            }
        });

        // Count active playlists
        dashboardDetails.playlist = await DocumentCategory.findOne({
            attributes: [
                [sequelize.fn('COUNT', sequelize.col('*')), 'total_playlist'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('category_status'), category_status.ACTIVE)), 'total_active'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('category_status'), category_status.DRAFT)), 'total_draft'],
                [sequelize.fn('SUM', sequelize.where(sequelize.col('category_status'), category_status.INACTIVE)), 'total_inactive']
            ],
            where: {
                organization_id: req.user.organization_id,
                category_status: {
                    [Op.notIn]: [category_status.DELETED]
                },
                parent_id: null
            } as any,
            raw: true
        });
        // Count incomplete onboarding
        dashboardDetails.total_incomplete_onboarding = await User.count({
            where: {
                user_status: {
                    [Op.in]: [user_status.PENDING, user_status.ACTIVE, user_status.ONGOING]
                },
                id: { [Op.not]: excludedUserIds },
                organization_id: req.user.organization_id
            }
        });

        // Count pending leave requests
        dashboardDetails.total_pending_leave_request = await UserRequest.count({
            include: [{
                model: User,
                as: 'user_request_user',
                where: { organization_id: req.user.organization_id }
            }],
            where: { request_status: request_status.PENDING }
        });

        // Count pending resignations
        dashboardDetails.total_pending_resignation = await Resignation.count({
            include: [{
                model: User,
                as: 'resignation_user',
                where: { organization_id: req.user.organization_id }
            }],
            where: { resignation_status: resignation_status.PENDING }
        });

        // Count completed onboarding
        dashboardDetails.total_completed_onboarding = await User.count({
            where: {
                user_status: {
                    [Op.in]: [user_status.COMPLETED]
                },
                id: { [Op.not]: excludedUserIds },
                organization_id: req.user.organization_id
            }
        });

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: dashboardDetails
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error
        });
    }
};


/**
 * Get App Dashboard Detail
 * @param req
 * @param res
 * @returns
 */

const getAppDashboardDetail = async (req: any, res: any) => {
    try {
        const { size, page, }: any = req.query;
        const { limit, offset } = getPagination(Number(page), Number(size));

        const categotyObj: any = {
            attributes: [
                'id',
                'category_name',
                'category_description',
                "category_status",
                "dashboard_view",
                [
                    sequelize.literal(
                        `(
                        SELECT
                        (CASE
                            WHEN ((ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))) = 100) THEN 'completed'
                            WHEN ((ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))) = 0) THEN 'pending'
                            ELSE 'ongoing'
                        END)
                        FROM nv_playlist_media
                        WHERE playlist_media_status = 'active' 
                            AND playlist_id IN ( SELECT id FROM nv_playlist WHERE id IN( SELECT
                                    playlist_id
                                FROM
                                    nv_playlist_category
                                WHERE
                                    category_id = Category.id AND playlist_category_status = 'active'
                            ) AND id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id =  ${req.user.branch_id} AND playlist_branch_status = 'active') AND 
                            id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = ${req.user.department_id} AND playlist_department_status = 'active') 
                            AND playlist_status = 'active'
                        )
                    )`,
                    ),
                    "category_pl_track_status",
                ],
                [
                    sequelize.literal(
                        `(
                        SELECT
                            ROUND(((SUM((SELECT count(*) FROM nv_playlist_media_track WHERE playlist_id = nv_playlist_media.playlist_id AND media_id = nv_playlist_media.media_id AND playlist_media_track_status = 'completed' AND user_id = ${req.user.id})))*100)/COUNT(*))
                        FROM nv_playlist_media
                        WHERE playlist_media_status = 'active' 
                            AND playlist_id IN ( SELECT id FROM nv_playlist WHERE id IN( SELECT
                                    playlist_id
                                FROM
                                    nv_playlist_category
                                WHERE
                                    category_id = Category.id AND playlist_category_status = 'active'
                            ) AND id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id =  ${req.user.branch_id} AND playlist_branch_status = 'active') AND 
                            id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = ${req.user.department_id} AND playlist_department_status = 'active') 
                            AND playlist_status = 'active'
                        )
                    )`,
                    ),
                    "category_pl_track_percentage",
                ]
            ],
            where: {
                dashboard_view: true,
                category_status: category_status.ACTIVE
            },
            raw: true,
            nest: true
        }

        categotyObj.where[Op.and] = [
            sequelize.literal(`(
            SELECT
              count(*)
            FROM nv_playlist_media
            WHERE playlist_media_status = 'active' 
                AND playlist_id IN ( 
                SELECT id FROM nv_playlist 
                WHERE id IN (SELECT playlist_id FROM nv_playlist_category WHERE category_id = Category.id AND playlist_category_status = 'active' AND playlist_id = nv_playlist.id) 
                AND id IN (SELECT playlist_id FROM nv_playlist_branch WHERE branch_id =  ${req.user.branch_id} AND playlist_branch_status = 'active' AND playlist_id = nv_playlist.id) 
                AND id IN (SELECT playlist_id FROM nv_playlist_department WHERE department_id = ${req.user.department_id} AND playlist_department_status = 'active' AND playlist_id = nv_playlist.id) 
                AND playlist_status = 'active'
            )
        ) > 0`)
        ]

        if (page && size) {
            categotyObj.limit = Number(limit);
            categotyObj.offset = Number(offset);
        }

        const getCategoryDetail = await Category.findAndCountAll(categotyObj)

        const { total_pages } = getPaginatedItems(size, page, getCategoryDetail.count || 0);

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getCategoryDetail.rows,
            page: parseInt(page),
            size: parseInt(size),
            count: getCategoryDetail.count,
            total_pages,
        });


    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


const getModelType = async (req: any, res: any) => {
    try {
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: await getDashboardModel()
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


const getModelTypeTab = async (req: any, res: any) => {
    try {
        // Get the start and end dates for the last 5 years
        const startDateYear = moment().subtract(4, 'years').startOf('year').format('YYYY-MM-DD') // Start of 5 years ago
        const endDateYear = moment().endOf('year').format('YYYY-MM-DD') // End of the current year
        // Prepare the response object with default values
        const dataObj: any = {
            // Fetch active branches with specific attributes
            branch: await Branch.findAll({
                attributes: ['id', 'branch_name', 'branch_color', 'branch_status'],
                where: { organization_id: req.user.organization_id, branch_status: { [Op.notIn]: [branch_status.DELETED] } }, raw: true
            }) || [],

            // Generate daily date components for the current date
            daily: await generateDateComponents(moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD'), 'daily') || {},

            // Generate weekly date components for the current date
            weekly: await generateDateComponents(moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD'), 'weekly') || {},

            // Generate quarterly date components for the current date
            quarterly: await generateDateComponents(moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD'), 'quarterly') || {},

            // Generate monthly date components for the current date
            monthly: await generateDateComponents(moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD'), 'monthly') || {},
            // Generate monthly date components for the current date
            yearly: await generateDateComponents(startDateYear, endDateYear, 'yearly') || {},

            // Static object for numeric mapping of terms
            number: {
                dsr: 'Dsr',
                wsr: 'Wsr',
                expense: 'Expense',
                branch: 'Branch',
                category: 'Category',
                department: 'Department',
                user: 'User'
            },

            // Fetch DSR report data
            dsr: await getReportDashboard('dsr', req.user.organization_id) || [],

            // Fetch WSR report data
            wsr: await getReportDashboard('wsr', req.user.organization_id) || [],

            // Fetch expense report data
            expense: await getReportDashboard('expense', req.user.organization_id) || [],
        };

        // Return the successfully fetched data
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: dataObj,
        });
    } catch (error) {
        console.error("Error in getModelTypeTab:", error); // Log the error for debugging

        // Handle and return an error response
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


const getDashboardList = async (req: any, res: any) => {
    try {
        const findDashboard = await Dashboard.findAll({
            attributes: ['id', 'dashboard_name', 'has_dashboard_default', 'dashboard_status'], where: { dashboard_status: dashboard_status.ACTIVE, organization_id: req.user.organization_id },
            include: [
                {
                    model: User,
                    as: 'users',
                    where: { organization_id: req.user.organization_id },
                    attributes: [] // Exclude user attributes if not needed in response
                }
            ],
            raw: false,  // Remove raw mode
            nest: true   // Ensure nested structure
        })

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: findDashboard.length > 0 ? findDashboard : []
        });

    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }

}

const createDashboard = async (req: any, res: any) => {
    try {
        const { dashboard_name, dashboard_filter, model_list = [] } = req.body
        const findDashboard = await Dashboard.findOne({ attributes: ['id'], where: { dashboard_name: dashboard_name, dashboard_status: dashboard_status.ACTIVE, organization_id: req.user.organization_id }, raw: true })
        if (findDashboard) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("DASHBOARD_NAME_ALREADY_EXISTS"),
            });
        }
        let dashboardDetails: any = false
        const findAllDashboard = await Dashboard.findOne({ attributes: ['id'], where: { dashboard_status: dashboard_status.ACTIVE, has_dashboard_default: true, organization_id: req.user.organization_id }, raw: true })
        if (!findAllDashboard) {
            dashboardDetails = true
        }
        const addDashboard = await Dashboard.create({ dashboard_name: dashboard_name, has_dashboard_default: dashboardDetails, dashboard_status: dashboard_status.ACTIVE, dashboard_filter: dashboard_filter, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id, organization_id: req.user.organization_id } as any)
        if (addDashboard) {
            if (model_list.length > 0) {
                for (let index = 0; index < model_list.length; index++) {
                    const model = model_list[index];
                    await DashboardModel.create({
                        dashboard_id: addDashboard.id,
                        model_title: model.model_title,
                        xaxis_list: model.xaxis_list,
                        yaxis_list: model.yaxis_list,
                        xaxis_value: model.xaxis_value,
                        yaxis_value: model.yaxis_value,
                        created_by: req.user.id,
                        updated_by: req.user.id,
                        model_status: model_status.ACTIVE,
                        model_type: model.model_type,
                        model_order: index + 1 // Assigning order based on array position (1-based index)
                    } as any);
                }
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("DASHBOARD_SAVED_SUCCESSFULLY"),
                    dashboard_id: addDashboard.id
                });
            } else {
                return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("FAIL_TO_SAVE_DASHBOARD"),
                });
            }
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }

}

const updateDashboard = async (req: any, res: any) => {
    try {
        const { dashboard_name, dashboard_filter, model_list = [] } = req.body
        const { id } = req.params
        const findDashboardExist: any = await Dashboard.findOne({ attributes: ['id'], where: { dashboard_name: dashboard_name, dashboard_status: dashboard_status.ACTIVE, organization_id: req.user.organization_id }, raw: true })
        if (findDashboardExist && findDashboardExist.id != id) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({
                status: false,
                message: res.__("DASHBOARD_NAME_ALREADY_EXISTS"),
            });
        }
        const getDashboardData: any = await Dashboard.findOne({ attributes: ['id'], where: { id: id, organization_id: req.user.organization_id }, raw: true })
        const updateDashboard = await Dashboard.update({ dashboard_name: dashboard_name, dashboard_status: dashboard_status.ACTIVE, dashboard_filter: dashboard_filter, user_id: req.user.id, created_by: req.user.id, updated_by: req.user.id } as any, { where: { id: id } })

        if (updateDashboard.length > 0) {
            if (model_list.length > 0) {
                const model_ids = [];

                for (const model of model_list) {
                    if (model?.id) {
                        // Find existing model by ID
                        const getModelData: any = await DashboardModel.findOne({
                            attributes: ['id'],
                            where: { id: model.id, model_status: model_status.ACTIVE }, raw: true
                        });

                        if (getModelData) {
                            model_ids.push(getModelData.id);
                            // Update existing model
                            await DashboardModel.update(
                                {
                                    model_title: model.model_title,
                                    xaxis_list: model.xaxis_list,
                                    yaxis_list: model.yaxis_list,
                                    xaxis_value: model.xaxis_value,
                                    yaxis_value: model.yaxis_value,
                                    model_type: model.model_type,
                                    model_order: model.model_order, // Update order explicitly
                                    updated_by: req.user.id
                                },
                                { where: { id: getModelData.id } }
                            );
                        } else {
                            // Create new model for invalid ID
                            const addDashboardModel = await DashboardModel.create({
                                dashboard_id: getDashboardData.id,
                                model_title: model.model_title,
                                xaxis_list: model.xaxis_list,
                                yaxis_list: model.yaxis_list,
                                xaxis_value: model.xaxis_value,
                                yaxis_value: model.yaxis_value,
                                created_by: req.user.id,
                                updated_by: req.user.id,
                                model_status: model_status.ACTIVE,
                                model_type: model.model_type,
                                model_order: model.model_order // Set order explicitly
                            } as any);
                            model_ids.push(addDashboardModel.id);
                        }
                    } else {
                        // Create new model for missing ID
                        const addDashboardModel = await DashboardModel.create({
                            dashboard_id: getDashboardData.id,
                            model_title: model.model_title,
                            xaxis_list: model.xaxis_list,
                            yaxis_list: model.yaxis_list,
                            xaxis_value: model.xaxis_value,
                            yaxis_value: model.yaxis_value,
                            created_by: req.user.id,
                            updated_by: req.user.id,
                            model_status: model_status.ACTIVE,
                            model_type: model.model_type,
                            model_order: model.model_order || model_list.indexOf(model) + 1 // Default to index if order is not provided
                        } as any);
                        model_ids.push(addDashboardModel.id);
                    }
                }

                // Mark models not in the current model list as deleted
                if (model_ids.length > 0) {
                    await DashboardModel.update(
                        { model_status: model_status.DELETED },
                        { where: { dashboard_id: getDashboardData.id, id: { [Op.notIn]: model_ids } } }
                    );
                }
            }

            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("DASHBOARD_UPDATED_SUCCESSFULLY"),
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_TO_UPDATE_DASHBOARD"),
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }

}

const markDashboardDefault = async (req: any, res: any) => {
    try {
        const { id } = req.params
        const updateDashboard = await Dashboard.update({ has_dashboard_default: true, updated_by: req.user.id } as any, { where: { id: id, dashboard_status: dashboard_status.ACTIVE, organization_id: req.user.organization_id } })
        if (updateDashboard.length > 0) {
            await Dashboard.update({ has_dashboard_default: false, updated_by: req.user.id } as any, { where: { id: { [Op.ne]: id }, dashboard_status: dashboard_status.ACTIVE, organization_id: req.user.organization_id } })
            return res.status(StatusCodes.OK).json({
                status: true,
                message: res.__("MARK_DASHBOARD_DEFAULT"),
            });
        } else {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("FAIL_TO_MARK_DASHBOARD"),
            });
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const deleteDashboard = async (req: any, res: any) => {
    try {
        const { id } = req.params
        const findDashboard: any = await Dashboard.findOne({ attributes: ['id', 'has_dashboard_default'], where: { id: id, dashboard_status: dashboard_status.ACTIVE, organization_id: req.user.organization_id }, raw: true })
        if (findDashboard && findDashboard.has_dashboard_default) {
            return res.status(StatusCodes.EXPECTATION_FAILED).json({ status: false, message: res.__("FAIL_TO_DELETE_DEFAULT_DASHBOARD") });
        } else {
            const deleteDashboard = await Dashboard.update({ dashboard_status: dashboard_status.DELETED, updated_by: req.user.id } as any, { where: { id: id } })
            if (deleteDashboard.length > 0) {
                await DashboardModel.update({ model_status: model_status.DELETED, updated_by: req.user.id } as any, { where: { dashboard_id: id } })
                return res.status(StatusCodes.OK).json({
                    status: true,
                    message: res.__("DASHBOARD_DELETED_SUCCESSFULLY"),
                });
            } else {
                return res.status(StatusCodes.BAD_REQUEST).json({
                    status: false,
                    message: res.__("FAIL_TO_DELETE_DASHBOARD"),
                });
            }
        }
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

const getDashboardById = async (req: any, res: any) => {
    try {
        const { id } = req.params
        const { branch_id, date_filter, filter_time_period }: any = req.query
        let getDashboardData: any = await Dashboard.findOne({
            attributes: ['id', 'dashboard_name', 'dashboard_status', 'has_dashboard_default', 'dashboard_filter'],
            include: [{
                model: DashboardModel,
                as: 'dashboard_models',
                attributes: ['id', 'model_title', 'model_status', 'xaxis_list', 'yaxis_list', 'xaxis_value', 'yaxis_value', 'model_type', 'model_order'],
                where: { model_status: model_status.ACTIVE }
            }],
            where: { id: id, dashboard_status: dashboard_status.ACTIVE, organization_id: req.user.organization_id }, nest: true,
            order: [["dashboard_models", "model_order", "ASC"]]
        })
        if (getDashboardData) {
            getDashboardData = JSON.parse(JSON.stringify(getDashboardData))

            if (getDashboardData && getDashboardData?.dashboard_models.length > 0) {
                for (const model of getDashboardData.dashboard_models) {
                    const getDashboardModelData: any = await DashboardModel.findOne({ attributes: ['id'], where: { id: model.id, model_status: model_status.ACTIVE }, raw: true })
                    if (getDashboardModelData) {
                        const dashboardFilter = JSON.parse(getDashboardData.dashboard_filter)
                        const branchId = branch_id ? branch_id : (typeof dashboardFilter.branch_id !== 'undefined' && dashboardFilter.branch_id ? dashboardFilter.branch_id : null)
                        const dateFilter = date_filter ? date_filter : (typeof dashboardFilter.date_filter !== 'undefined' && dashboardFilter.date_filter ? dashboardFilter.date_filter : null)
                        const filterTimePeriod = filter_time_period ? filter_time_period : (typeof dashboardFilter.filter_time_period !== 'undefined' && dashboardFilter.filter_time_period ? dashboardFilter.filter_time_period : null)
                        model.dashboard_data = await generateDashboard(branchId, null, null, dateFilter, getDashboardModelData.id, filterTimePeriod, req.user.organization_id)
                    }
                }
            } else {
                getDashboardData.dashboard_models = []
            }
        }
        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: getDashboardData || {}
        });
    } catch (error) {
        console.log(error);
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
}

/**
 * Get Dashboard Widgets by Type
 * @param req
 * @param res
 * @returns
 */
const getDashboardWidgets= async (req: any, res: any) => {
    try {
        const { dashboard_type } = req.body;

        // Validate dashboardType parameter
        if (!dashboard_type) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("DASHBOARD_TYPE_REQUIRED"),
                data: null,
            });
        }

        // Validate supported dashboard types
        const supportedTypes = ['user', 'dsr', 'setup'];
        if (!supportedTypes.includes(dashboard_type.toLowerCase())) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_DASHBOARD_TYPE"),
                data: {
                    supportedTypes: supportedTypes
                },
            });
        }

        // Get dashboard widgets based on type using switch case
        let widgets = [];

        switch (dashboard_type.toLowerCase()) {
            case 'user':
                widgets = await getUserDashboardWidgets(
                    req.user.organization_id,
                    req.user.id,
                    req.body
                );
                break;
            case 'dsr':
                widgets = await getDsrDashboardWidgets(
                    req.user.organization_id,
                    req.user.id,
                    req.body
                );
                break;
            case 'setup':
                widgets = await getSetupDashboardWidgets(
                    req.user.organization_id,
                    req.user.id,
                    req.body
                );
                break;
            default:
                throw new Error(`Unsupported dashboard type: ${dashboard_type}`);
        }

        const dashboardWidgets = {
            dashboardType: dashboard_type,
            widgets,
            totalWidgets: widgets.length
        };

        return res.status(StatusCodes.OK).json({
            status: true,
            message: res.__("SUCCESS_FETCHED"),
            data: dashboardWidgets,
        });

    } catch (error: any) {
        console.error("Error in getDashboardWidgetsByTypeController:", error);

        // Handle specific error types
        if (error.message && error.message.includes('Unsupported dashboard type')) {
            return res.status(StatusCodes.BAD_REQUEST).json({
                status: false,
                message: res.__("INVALID_DASHBOARD_TYPE"),
                data: error.message,
            });
        }

        // Handle general errors
        return res.status(StatusCodes.SERVICE_UNAVAILABLE).json({
            status: false,
            message: res.__("SOMETHING_WENT_WRONG"),
            data: error,
        });
    }
};


export default {
    getAdminDashboardDetail,
    getAppDashboardDetail,
    getModelType,
    getModelTypeTab,
    getDashboardList,
    createDashboard,
    updateDashboard,
    markDashboardDefault,
    deleteDashboard,
    getDashboardById,
    getDashboardWidgets
};

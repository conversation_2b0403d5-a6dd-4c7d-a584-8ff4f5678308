import { Request, Response, NextFunction } from "express";
import jwt from "jsonwebtoken";
import { User, user_status } from "../models/User";
import { Op } from "sequelize";
import { StatusCodes } from "http-status-codes";
import { ADMIN_SIDE_USER, NORMAL_USER } from "../helper/constant";
import { UserRole } from "../models/UserRole";
import { Role } from "../models/Role";
import _ from "lodash";
import { UserSession } from "../models/UserSession";

const userAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // checking if token is present in header
    if (!req.headers.authorization) {
      return res
        .status(403)
        .send({ status: false, message: res.__("ERR_TOKEN_NOT_FOUND") });
    }
    // if (!req.cookies) {
    //   res.status(403).send({ message: "Unauthorized request" });
    //   return;
    // }

    // const sessionToken: string = req.cookies["refresh_token"];

    // if (!sessionToken) {
    //   res.status(403).send({ message: "Unauthorized request" });
    //   return;
    // }

    // const refreshVerify = jwt.verify(
    //   sessionToken,
    //   global.config.REFRESH_TOKEN_SECRET,
    // );
    // if (!refreshVerify) {
    //   return res.status(403).send({ message: "Unauthorized request" });
    // }
    // if (!refreshVerify.id) {
    //   return res.status(403).send({ message: "Unauthorized request" });
    // }
    // removing Bearer keyword
    const token: string = req.headers.authorization?.replace("Bearer ", "");

    // verifying token
    const decoded = jwt.verify(token, global.config.JWT_SECRET);

    // if (refreshVerify.id != decoded.id) {
    //   return res.status(403).send({ message: "Unauthorized request" });
    // }
    // finding user with details decoded fro token
    const getUserDetail: any = await User.findOne({
      where: {
        // id: decoded.id,
        keycloak_auth_id: decoded.user_id,
        user_status: {
          [Op.not]: [
            user_status.CANCELLED,
            user_status.DELETED
          ],
        },
      },
      raw: true,
      nest: true,
    });
    if (!getUserDetail) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ status: false, message: res.__("ERROR_USER_NOT_FOUND") });
    }
    const userRoles = await UserRole.findAll({
      where: { user_id: getUserDetail.id },
      include: [
        {
          model: Role,
          as: "role",
          attributes: ["id", "role_name"],
        },
      ],
      nest: true,
      raw: true,
    });
    const loginUserRoles = (userRoles.length > 0
      ? _.map(userRoles, (userRole: any) => userRole.role.role_name)
      : [])

    const normalUser = [...NORMAL_USER];
    const adminSideUser = [...ADMIN_SIDE_USER];

    if (
      !loginUserRoles.some((item: any) => adminSideUser.includes(item)) &&
      req.headers["platform-type"] == "web"
    ) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ status: false, message: res.__("FAIL_TOKEN_EXPIRED") });
    }

    if (
      !loginUserRoles.some((item: any) => normalUser.includes(item)) &&
      (req.headers["platform-type"] == "ios" ||
        req.headers["platform-type"] == "android")
    ) {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ status: false, message: res.__("FAIL_TOKEN_EXPIRED") });
    }
    if (userRoles.length > 0) {
      const roleData: any = userRoles[0]

      if (process.env.NEXT_NODE_ENV !== "staging" && roleData.role.name !== 'Super Admin') {
        let deviceType = req.headers["platform-type"];
        if (!deviceType) {
          return res
            .status(StatusCodes.BAD_REQUEST)
            .send({
              status: false,
              message: res.__("ERROR_PLATEFORM_TYPE_REQUIRED"),
            });
        }

        deviceType = deviceType == 'ios' ? 'android' : deviceType
        const session = await UserSession.findOne({ where: { token, device_type: deviceType }, attributes: ['id'], raw: true });
        if (!session) {
          return res
            .status(StatusCodes.UNAUTHORIZED)
            .json({ status: false, message: res.__("FAIL_TOKEN_EXPIRED") });
        }
      }
    }


    if (getUserDetail) {
      if (getUserDetail.token_version && getUserDetail.token_version != 0) {
        if (getUserDetail.token_version != decoded.token_version) {
          return res
            .status(StatusCodes.UNAUTHORIZED)
            .send({
              status: false,
              message: res.__("USER_PASSOWORD_RESET_RE_LOGIN"),
            });
        }
      }
    }

    if (req.headers["platform-type"] == "ios" || req.headers["platform-type"] == "android") {
      if (getUserDetail) {
        if (getUserDetail.pin_token_version && getUserDetail.pin_token_version != 0) {
          if (getUserDetail.pin_token_version != decoded.pin_token_version) {
            return res
              .status(StatusCodes.UNAUTHORIZED)
              .send({
                status: false,
                message: res.__("USER_PIN_RESET_RE_LOGIN"),
              });
          }
        }
      }
    }

    req.user = getUserDetail;

    next();
  } catch (e: any) {
    console.log(e);
    if (e.message == "jwt malformed") {
      return res
        .status(StatusCodes.UNAUTHORIZED)
        .send({ status: false, message: res.__("ERR_TOKEN_NOT_FOUND") });
    } else {
      if (e.name == "TokenExpiredError") {
        return res
          .status(StatusCodes.UNAUTHORIZED)
          .send({
            status: false,
            message: res.__("FAIL_TOKEN_EXPIRED"),
          });
      } else {
        return res.status(401).send({ status: false, message: e.message });
      }
    }
  }
};

export default userAuth;
